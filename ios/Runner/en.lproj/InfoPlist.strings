/* 
  InfoPlist.strings
  Runner

  Created for English localization.
*/

// Camera permission description
"NSCameraUsageDescription" = "This app requires camera access to take photos for profile pictures and sharing with friends.";

// Local network permission description
"NSLocalNetworkUsageDescription" = "This app requires local network access for debugging and development features.";

// Photo library read permission description
"NSPhotoLibraryUsageDescription" = "This app requires photo library access to select images for profile pictures and sharing with friends.";

// Photo library write permission description
"NSPhotoLibraryAddUsageDescription" = "This app requires photo library access to save images to your photo album.";

// Microphone permission description
"NSMicrophoneUsageDescription" = "This app requires microphone access to record voice messages for chatting with friends.";

// Location permission description (when in use)
"NSLocationWhenInUseUsageDescription" = "This app requires location access to show your current location on the map and provide related services.";

// Location permission description (always and when in use)
"NSLocationAlwaysAndWhenInUseUsageDescription" = "This app requires background location access to provide continuous location services.";

// Location permission description (always)
"NSLocationAlwaysUsageDescription" = "This app requires constant location access to provide better service experience.";

// Notification permission description
"NSUserNotificationsUsageDescription" = "This app requires notification permission to receive notifications for new messages and likes.";

// App name localization
"CFBundleDisplayName" = "URAMO";
"CFBundleName" = "URAMO";
