import 'dart:io';

import 'package:flutter/material.dart';
import 'package:foreign_friends/utils/PermissionUtil.dart';
import 'package:foreign_friends/utils/ToastUtils.dart';
import 'package:foreign_friends/utils/video/VideoFilterManager.dart';
import 'package:foreign_friends/utils/image/picker/AssetPickerConfigManager.dart';
import 'package:get/get.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

import '../../../vm/ChatVM.dart';

// 聊天相册选择器回调函数类型定义
typedef ChatAlbumSelectCallback = void Function(String imagePath);

/// 聊天相册选择器
/// 专门处理聊天页面的单张图片和视频选择功能
class ChatAlbumSelector {
  final PermissionUtil permissionUtil = PermissionUtil();

  /// 从相册选择单张图片或视频
  /// [context] 上下文
  /// [vm] ChatVM实例，用于回调
  Future<void> selectSingleMedia({
    required BuildContext context,
    required ChatVM vm,
  }) async {
    // 请求相册权限
    bool hasPermission = await permissionUtil.requestPhotoPermission(context);
    if (!hasPermission) {
      ToastUtils().showToast('photo_permission_denied'.tr);
      return;
    }

    try {
      // 创建选择器配置（在异步调用前）
      final pickerConfig =
          AssetPickerConfigManager.createChatSingleMediaPickerConfig(context);

      // 使用 AssetPicker 选择单张图片或视频
      final List<AssetEntity>? result = await AssetPicker.pickAssets(
        context,
        pickerConfig: pickerConfig,
      );

      // 处理选择结果
      if (result != null && result.isNotEmpty) {
        await handleSelectedMedia(result.first, vm);
      }
    } catch (e) {
      ToastUtils().showToast('select_media_failed'.tr);
      debugPrint('选择媒体失败: $e');
    }
  }

  /// 处理选中的媒体（图片或视频）
  /// [asset] 选中的媒体资源
  /// [vm] ChatVM实例
  Future<void> handleSelectedMedia(AssetEntity asset, ChatVM vm) async {
    try {
      // 获取媒体文件
      File? mediaFile = await asset.file;
      if (mediaFile != null && mediaFile.existsSync()) {
        // 根据媒体类型处理
        if (asset.type == AssetType.image) {
          // 图片消息
          vm.sendPhotoMessage(mediaFile.path);
          debugPrint('选择图片成功: ${mediaFile.path}');
        } else if (asset.type == AssetType.video) {
          // 视频消息，先检查大小限制再发送
          final filteredVideos =
              await VideoFilterManager.instance.filterVideos([asset]);
          if (filteredVideos != null && filteredVideos.isNotEmpty) {
            final duration = asset.duration;
            vm.sendVideoMessage(mediaFile.path, duration);
            debugPrint('选择视频成功: ${mediaFile.path}, 时长: $duration秒');
          }
        }
      } else {
        ToastUtils().showToast('get_media_file_failed'.tr);
      }
    } catch (e) {
      ToastUtils().showToast('process_media_failed'.tr);
      debugPrint('处理媒体失败: $e');
    }
  }

  /// 保持向后兼容的图片选择方法
  /// [context] 上下文
  /// [vm] ChatVM实例，用于回调
  Future<void> selectSingleImage({
    required BuildContext context,
    required ChatVM vm,
  }) async {
    return selectSingleMedia(context: context, vm: vm);
  }
}
