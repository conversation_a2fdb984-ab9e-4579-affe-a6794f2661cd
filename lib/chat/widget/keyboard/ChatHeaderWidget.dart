import 'package:flutter/material.dart';
import 'package:foreign_friends/theme/AvatarImage.dart';
import 'package:foreign_friends/theme/ColorNatural.dart';
import 'package:foreign_friends/theme/NameText.dart';
import 'package:foreign_friends/utils/image/ImageLoading.dart';
import 'package:foreign_friends/utils/image/ImagePath.dart';
import 'package:foreign_friends/utils/picker/DatePickerUtils.dart';
import 'package:get/get.dart';

import '../../dialog/ChatUserActionDialog.dart';
import '../../vm/ChatVM.dart';

// 聊天页面顶部导航栏组件
class ChatHeaderWidget extends StatelessWidget {
  final ChatVM vm;

  const ChatHeaderWidget({super.key, required this.vm});

  @override
  Widget build(BuildContext context) {
    return Container(
      // 添加状态栏高度的padding
      padding: EdgeInsets.only(
          top: MediaQuery.of(context).padding.top + 10,
          left: 10,
          right: 16,
          bottom: 10),
      decoration: BoxDecoration(
        color: ColorNatural.natural_16,
        boxShadow: [
          BoxShadow(
              color: Colors.black.withValues(alpha: 0.7),
              offset: Offset(0, 1),
              blurRadius: 4)
        ],
      ),

      child: Row(
        children: [
          // 返回按钮
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () => Get.back(),
            child: Container(
              padding: EdgeInsets.all(8),
              child: ImageLoading(
                imageUrl: ImagePath.path("ic_user_details_1.png"),
                width: 30,
                height: 30,
                fit: BoxFit.fill,
              ),
            ),
          ),

          SizedBox(width: 12),

          // 用户头像
          buildUserAvatar(),

          SizedBox(width: 12),

          // 用户信息
          Expanded(child: buildUserInfo()),

          SizedBox(width: 12),

          // 更多按钮
          buildMoreButton(),
        ],
      ),
    );
  }

  // 构建用户头像
  Widget buildUserAvatar() {
    return GetBuilder<ChatVM>(
      builder: (_) {
        final userDetails = vm.model.userDetails;
        return GestureDetector(
          onTap: () {
            // 点击头像跳转到用户详情页面
            if (userDetails?.id != null) {
              Get.toNamed('/UserDetailsPage',
                  arguments: {'userId': userDetails!.id.toString()});
            }
          },
          child: AvatarImage(
            imageUrl: userDetails?.head ?? "",
            width: 40,
            height: 40,
            fit: BoxFit.cover,
            isVip: userDetails?.isVip == 1,
            borderWidth: 2,
            borderPadding: 1,
          ),
        );
      },
    );
  }

  // 构建用户信息
  Widget buildUserInfo() {
    return GetBuilder<ChatVM>(
      builder: (_) {
        final userDetails = vm.model.userDetails;

        if (userDetails == null) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 80,
                height: 16,
                decoration: BoxDecoration(
                  color: ColorNatural.natural_15,
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              SizedBox(height: 4),
              Container(
                width: 120,
                height: 12,
                decoration: BoxDecoration(
                  color: ColorNatural.natural_15,
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
            ],
          );
        }

        // 计算年龄
        String age = "";
        if (userDetails.birthday != null && userDetails.birthday!.isNotEmpty) {
          try {
            final birthDate = DateTime.parse(userDetails.birthday!);
            age = "${DatePickerUtils.calculateAge(birthDate)}${'age_unit'.tr}";
          } catch (e) {
            age = "";
          }
        }

        // 地址信息
        String address = userDetails.address ?? "";
        if (address.isNotEmpty) {
          address = "・$address";
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // 用户昵称
            NameText(
              text: userDetails.nick ?? "",
              style: TextStyle(
                color: ColorNatural.natural_1,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
              isVip: userDetails.isVip == 1,
            ),

            SizedBox(height: 4),

            // 性别、年龄、地址信息
            Row(
              children: [
                // 性别图标 - 性别为0时不显示
                if (userDetails.sex != 0) ...[
                  Icon(
                    userDetails.sex == 1 ? Icons.male : Icons.female,
                    color: userDetails.sex == 1 ? Colors.blue : Colors.pink,
                    size: 14,
                  ),
                  SizedBox(width: 4),
                ],

                // 年龄和地址
                Flexible(
                  child: Text(
                    '$age$address',
                    style: TextStyle(
                      color: ColorNatural.natural_6,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  // 构建更多按钮
  Widget buildMoreButton() {
    return GestureDetector(
      onTap: () => onMoreButtonTap(),
      child: Container(
        padding: EdgeInsets.all(8),
        child: ImageLoading(
          imageUrl: ImagePath.path("ic_chat_3.png"),
          width: 24,
          height: 24,
          fit: BoxFit.fill,
        ),
      ),
    );
  }

  // 更多按钮点击事件
  void onMoreButtonTap() {
    final userDetails = vm.model.userDetails;
    if (userDetails?.id != null) {
      ChatUserActionDialog.show(
        context: Get.context!,
        userId: userDetails!.id.toString(),
        userName: userDetails.nick,
        isBlocked: userDetails.isBlack ?? false,
        onBlock: () => onBlockUser(),
        onReport: () => onReportUser(),
      );
    }
  }

  // 拉黑用户
  void onBlockUser() {
    // 调用VM的拉黑用户方法
    vm.blockUser();
  }

  // 举报用户
  void onReportUser() {
    // 调用VM的举报用户方法
    vm.reportUser();
  }
}
