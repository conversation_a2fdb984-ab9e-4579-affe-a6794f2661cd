class HttpConfig {
  //Host
  static const String baseUrl = 'https://open.uramoapp.com/';
  static const String webSocketUrl = "wss://open.uramoapp.com/";

  //oss相关配置
  static const String bucketName = "uramoapp";
  static const String ossEndpoint = "oss-ap-northeast-1.aliyuncs.com";

  // 协议相关URL配置
  static const String agreementBaseUrl = 'https://doc.uramoapp.com/';

  // 利用规约
  static const String termsOfServiceUrl = '${agreementBaseUrl}use.html';

  // 隐私政策
  static const String privacyPolicyUrl = '${agreementBaseUrl}ys.html';

  // 年龄确认
  static const String ageVerificationUrl = '${agreementBaseUrl}nl.html';

  // 应用内课金政策
  static const String inAppPurchasePolicyUrl = '${agreementBaseUrl}sf.html';

  // 内容审查政策
  static const String contentReviewPolicyUrl = '${agreementBaseUrl}sh.html';

  // 特定商取引法表记
  static const String commercialTransactionActUrl =
      '${agreementBaseUrl}jyf.html';

  // 审核状态配置
  static bool isInAudit = false; // 是否处于审核状态，默认为false
}
