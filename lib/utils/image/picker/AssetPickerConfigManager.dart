import 'package:flutter/material.dart';
import 'package:foreign_friends/chat/widget/keyboard/album/CustomChatAssetPickerTextDelegate.dart';
import 'package:foreign_friends/utils/image/CustomAssetPickerTextDelegate.dart';
import 'package:get/get.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

import '../../../theme/ColorNatural.dart';

/// 相册选择器配置管理类
/// 统一管理所有相册选择器的配置，方便维护和修改
///
/// 使用场景说明：
/// - createMediaPickerConfig: 论坛发帖时选择图片和视频 (NormalAddAlbumSelector.dart)
/// - createPhotosPickerConfig: 论坛发帖时只选择图片 (NormalAddAlbumSelector.dart)
/// - createVideoPickerConfig: 论坛发帖时只选择视频 (NormalAddAlbumSelector.dart)
/// - createChatSingleMediaPickerConfig: 聊天时选择单张图片或视频 (ChatAlbumSelector.dart)
/// - createSingleImagePickerConfig: 头像选择等单图场景 (ImagePickerSingle.dart)
/// - createGeneralPickerConfig: 通用多图选择 (ImageMultipleSelect.dart)
class AssetPickerConfigManager {
  // 性能优化配置常量 - 这些参数直接影响内存使用和加载速度
  static const int gridCount = 4; // 每行显示4张照片 (影响UI布局)

  /// 创建媒体选择器配置（支持图片和视频）
  /// [context] 上下文
  /// [maxAssets] 最大选择数量，默认9张
  /// [selectedAssets] 已选择的资源列表
  static AssetPickerConfig createMediaPickerConfig(
    BuildContext context, {
    int maxAssets = 9,
    List<AssetEntity>? selectedAssets,
  }) {
    return AssetPickerConfig(
      // 基础配置
      maxAssets: maxAssets,
      selectedAssets: selectedAssets,
      requestType: RequestType.common,
      // 支持图片和视频
      textDelegate: const CustomAssetPickerTextDelegate(),
      themeColor: Theme.of(context).primaryColor,
      // 性能优化配置
      gridCount: gridCount,

      // 排序和显示配置
      sortPathDelegate: SortPathDelegate.common,
      specialItemPosition: SpecialItemPosition.none,

      // 相册名称翻译构建器
      pathNameBuilder: (AssetPathEntity path) {
        if (path.isAll) {
          return 'all_media'.tr;
        }
        if (path.name == "Camera") {
          return 'camera'.tr;
        }
        if (path.name == "Screenshots") {
          return 'screenshots'.tr;
        }
        if (path.name == "Recents") {
          return 'recent_media'.tr;
        }
        return path.name;
      },
    );
  }

  /// 创建照片选择器配置（只支持图片）
  /// [context] 上下文
  /// [maxAssets] 最大选择数量，默认9张
  /// [selectedAssets] 已选择的资源列表
  static AssetPickerConfig createPhotosPickerConfig(
    BuildContext context, {
    int maxAssets = 9,
    List<AssetEntity>? selectedAssets,
  }) {
    return AssetPickerConfig(
      // 基础配置
      maxAssets: maxAssets,
      selectedAssets: selectedAssets,
      requestType: RequestType.image,
      // 只支持图片
      textDelegate: const CustomAssetPickerTextDelegate(),
      themeColor: Theme.of(context).primaryColor,

      // 性能优化配置
      gridCount: gridCount,

      // 排序和显示配置
      sortPathDelegate: SortPathDelegate.common,
      specialItemPosition: SpecialItemPosition.none,

      // 相册名称翻译构建器
      pathNameBuilder: (AssetPathEntity path) {
        if (path.isAll) {
          return 'all_images'.tr;
        }
        if (path.name == "Camera") {
          return 'camera'.tr;
        }
        if (path.name == "Screenshots") {
          return 'screenshots'.tr;
        }
        if (path.name == "Recents") {
          return 'recent_images'.tr;
        }
        return path.name;
      },
    );
  }

  /// 创建视频选择器配置（只支持视频）
  /// [context] 上下文
  /// [maxAssets] 最大选择数量，默认1个
  static AssetPickerConfig createVideoPickerConfig(
    BuildContext context, {
    int maxAssets = 1,
  }) {
    return AssetPickerConfig(
      // 基础配置
      maxAssets: maxAssets,
      requestType: RequestType.video,
      // 只支持视频
      textDelegate: const CustomAssetPickerTextDelegate(),
      themeColor: Theme.of(context).primaryColor,

      // 性能优化配置
      gridCount: gridCount,

      // 视频格式配置
      filterOptions: FilterOptionGroup(
        videoOption: const FilterOption(), // 允许任意时长的视频
      ),

      // 排序和显示配置
      sortPathDelegate: SortPathDelegate.common,

      // 相册名称翻译构建器
      pathNameBuilder: (AssetPathEntity path) {
        if (path.isAll) {
          return 'all_videos'.tr;
        }
        if (path.name == "Camera") {
          return 'camera'.tr;
        }
        if (path.name == "Screenshots") {
          return 'screenshots'.tr;
        }
        if (path.name == "Recents") {
          return 'recent_videos'.tr;
        }
        return path.name;
      },
    );
  }

  /// 创建聊天单张媒体选择器配置（支持图片和视频）
  /// [context] 上下文
  static AssetPickerConfig createChatSingleMediaPickerConfig(BuildContext context) {
    return AssetPickerConfig(
      // 基础配置
      maxAssets: 1,
      // 聊天只能选择1张
      requestType: RequestType.common,
      // 支持图片和视频
      textDelegate: CustomChatAssetPickerTextDelegate(),
      themeColor: Theme.of(context).primaryColor,
      specialPickerType: null,
      // 性能优化配置
      gridCount: gridCount,
      // 排序和显示配置
      sortPathDelegate: SortPathDelegate.common,

      // 相册名称翻译构建器
      pathNameBuilder: (AssetPathEntity path) {
        if (path.isAll) {
          return 'all_media'.tr;
        }
        if (path.name == "Camera") {
          return 'camera'.tr;
        }
        if (path.name == "Screenshots") {
          return 'screenshots'.tr;
        }
        if (path.name == "Recents") {
          return 'recent_media'.tr;
        }
        return path.name;
      },
    );
  }

  /// 创建单图片选择器配置（头像等场景）
  /// [context] 上下文
  /// [themeColor] 主题色，可选
  static AssetPickerConfig createSingleImagePickerConfig(
    BuildContext context, {
    Color? themeColor,
  }) {
    return AssetPickerConfig(
      // 基础配置
      maxAssets: 1,
      requestType: RequestType.image,
      // 只支持图片
      textDelegate: const CustomAssetPickerTextDelegate(),
      themeColor: themeColor ?? ColorNatural.natural_4,
      specialPickerType: SpecialPickerType.noPreview,
      // 性能优化配置
      gridCount: gridCount,

      // 相册名称翻译构建器
      pathNameBuilder: (AssetPathEntity path) {
        if (path.isAll) {
          return 'all_images'.tr;
        }
        if (path.name == "Camera") {
          return 'camera'.tr;
        }
        if (path.name == "Screenshots") {
          return 'screenshots'.tr;
        }
        return path.name;
      },
    );
  }

  /// 创建通用选择器配置
  /// [context] 上下文
  /// [maxAssets] 最大选择数量
  /// [selectedAssets] 已选择的资源列表
  /// [requestType] 请求类型
  /// [themeColor] 主题色
  static AssetPickerConfig createGeneralPickerConfig(
    BuildContext context, {
    required int maxAssets,
    List<AssetEntity>? selectedAssets,
    RequestType requestType = RequestType.image,
    Color? themeColor,
  }) {
    return AssetPickerConfig(
      // 基础配置
      maxAssets: maxAssets,
      selectedAssets: selectedAssets,
      requestType: requestType,
      textDelegate: const CustomAssetPickerTextDelegate(),
      themeColor: themeColor ?? Colors.black,

      // 性能优化配置
      gridCount: gridCount,

      // 相册名称翻译构建器
      pathNameBuilder: (AssetPathEntity path) {
        if (path.isAll) {
          return requestType == RequestType.image
              ? 'all_images'.tr
              : requestType == RequestType.video
                  ? 'all_videos'.tr
                  : 'all_media'.tr;
        }
        if (path.name == "Camera") {
          return 'camera'.tr;
        }
        if (path.name == "Screenshots") {
          return 'screenshots'.tr;
        }
        return path.name;
      },
    );
  }
}
