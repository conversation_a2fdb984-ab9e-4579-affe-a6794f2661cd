import 'package:get/get.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';

// 自定义相机选择器的文本代理
class CustomCameraPickerTextDelegate extends CameraPickerTextDelegate {
  const CustomCameraPickerTextDelegate();

  @override
  String get confirm => 'camera_confirm_shoot'.tr;

  @override
  String get shoot => 'camera_shoot'.tr;

  @override
  String get record => 'camera_record'.tr;

  @override
  String get switchCameras => 'camera_switch'.tr;

  @override
  String get save => 'camera_save'.tr;

  @override
  String get delete => 'camera_delete'.tr;

  @override
  String get cancel => 'camera_cancel'.tr;

  @override
  String get retake => 'camera_retake'.tr;

  // 拍摄提示文本
  @override
  String get shootingTips => 'camera_shooting_tips'.tr;

  @override
  String get shootingWithRecordingTips => 'camera_shooting_with_recording_tips'.tr;

  @override
  String get shootingOnlyRecordingTips => 'camera_shooting_only_recording_tips'.tr;

  @override
  String get shootingTapRecordingTips => 'camera_shooting_tap_recording_tips'.tr;

  // 加载和保存提示
  @override
  String get loadFailed => 'camera_load_failed'.tr;

  @override
  String get loading => 'camera_loading'.tr;

  @override
  String get saving => 'camera_saving'.tr;
}
