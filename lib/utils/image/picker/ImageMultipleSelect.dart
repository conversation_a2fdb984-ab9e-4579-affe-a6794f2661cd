import 'package:flutter/material.dart';
import 'package:foreign_friends/utils/PermissionUtil.dart';
import 'package:foreign_friends/utils/ToastUtils.dart';
import 'package:foreign_friends/utils/video/VideoFilterManager.dart';
import 'AssetPickerConfigManager.dart';
import 'package:get/get.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class ImageMultipleSelect {
  final PermissionUtil permissionUtil = PermissionUtil();

  // 从相册选择多张图片
  Future<List<AssetEntity>?> selectImages(
    BuildContext context, {
    int maxAssets = 3,
    List<AssetEntity>? selectedAssets,
  }) async {
    try {
      // 检查context有效性
      if (!context.mounted) {
        debugPrint('Context已失效，无法选择图片');
        return null;
      }

      // 请求相册权限
      bool hasPermission = await permissionUtil.requestPhotoPermission(context);
      if (!hasPermission) {
        ToastUtils().showToast('photo_permission_denied'.tr);
        return null;
      }

      // 检查context是否仍然有效
      if (!context.mounted) {
        debugPrint('Context在权限请求后失效');
        return null;
      }

      final List<AssetEntity>? result = await AssetPicker.pickAssets(
        context,
        pickerConfig: AssetPickerConfigManager.createGeneralPickerConfig(
          context,
          maxAssets: maxAssets,
          selectedAssets: selectedAssets,
          requestType: RequestType.image,
        ),
      );

      return result;
    } catch (e) {
      debugPrint('多图片选择失败: $e');
      ToastUtils().showToast('multiple_images_select_failed'.tr);
      return null;
    }
  }

  // 从相册选择单个视频
  Future<List<AssetEntity>?> selectVideo(
    BuildContext context,
  ) async {
    try {
      // 检查context有效性
      if (!context.mounted) {
        debugPrint('Context已失效，无法选择视频');
        return null;
      }

      bool hasPermission = await permissionUtil.requestVideoPermission(context);
      if (!hasPermission) {
        ToastUtils().showToast('video_permission_denied'.tr);
        return null;
      }

      // 检查context是否仍然有效
      if (!context.mounted) {
        debugPrint('Context在权限请求后失效');
        return null;
      }

      final List<AssetEntity>? result = await AssetPicker.pickAssets(
        context,
        pickerConfig: AssetPickerConfigManager.createVideoPickerConfig(context),
      );

      // 使用VideoFilterManager筛选视频
      return VideoFilterManager.instance.filterVideos(result);
    } catch (e) {
      debugPrint('视频选择失败: $e');
      ToastUtils().showToast('video_select_failed'.tr);
      return null;
    }
  }
}
