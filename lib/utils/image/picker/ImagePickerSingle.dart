import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:foreign_friends/ui/login/dialog/CropAvatarDialog.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';

import '../../PermissionUtil.dart';
import '../../ToastUtils.dart';
import '../ImageCropUtil.dart';
import '../ImageOrientationUtil.dart';
import 'AssetPickerConfigManager.dart';
import 'CustomCameraPickerState.dart';
import 'CustomCameraPickerTextDelegate.dart';

// 图片路径选择回调函数类型
typedef ImagePathCallback = void Function(String imagePath);

// 图片选择器单选工具类
class ImagePickerSingle {
  // 权限工具实例
  final PermissionUtil permissionUtil = PermissionUtil();

  // 设备信息插件
  final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();

  // 当前摄像头类型（true=前置，false=后置）
  bool _currentIsFrontCamera = true;

  // 设置摄像头切换监听
  void _setupCameraLensChangeListener() {
    CustomCameraPickerState.onCameraLensChanged = (isFrontCamera) {
      _currentIsFrontCamera = isFrontCamera;
    };
  }

  // 从相册选择单张图片
  // [onSelected] 选择成功后的回调函数
  Future<void> selectFromGallery({
    required BuildContext context,
    required ImagePathCallback onSelected,
  }) async {
    try {
      // 检查context有效性
      if (!context.mounted) {
        debugPrint('Context已失效，无法选择图片');
        return;
      }

      // 请求相册权限（自动处理不同平台和Android版本）
      bool hasPermission = await permissionUtil.requestPhotoPermission(context);
      if (!hasPermission) {
        ToastUtils().showToast('photo_permission_denied'.tr);
        return;
      }

      final List<AssetEntity>? result = await AssetPicker.pickAssets(context, pickerConfig: AssetPickerConfigManager.createSingleImagePickerConfig(context));
      if (result != null && result.isNotEmpty) {
        File? imageFile = await result.first.file;
        if (imageFile != null && await imageFile.exists()) {
          onSelected(imageFile.path);
        } else {
          ToastUtils().showToast('image_file_not_exist'.tr);
        }
      }
    } catch (e) {
      debugPrint('相册选择失败: $e');
      ToastUtils().showToast('gallery_select_failed'.tr);
    }
  }

  // 从相册选择图片、裁剪后显示预览确认对话框
  // [onCropComplete] 确认后的回调函数
  Future<void> selectCropAndPreview({
    required BuildContext context,
    required Function(String) onCropComplete,
  }) async {
    try {
      // 检查context有效性
      if (!context.mounted) {
        debugPrint('Context已失效，无法选择图片');
        return;
      }

      // 请求相册权限
      bool hasPermission = await permissionUtil.requestPhotoPermission(context);
      if (!hasPermission) {
        ToastUtils().showToast('photo_permission_denied'.tr);
        return;
      }

      // 检查context是否仍然有效
      if (!context.mounted) {
        debugPrint('Context在权限请求后失效');
        return;
      }

      // 从相册选择图片
      final List<AssetEntity>? result = await AssetPicker.pickAssets(context, pickerConfig: AssetPickerConfigManager.createSingleImagePickerConfig(context));
      if (result != null && result.isNotEmpty) {
        File? imageFile = await result.first.file;
        if (imageFile != null && await imageFile.exists()) {
          // 检查context是否仍然有效
          if (!context.mounted) {
            debugPrint('Context在图片获取后失效');
            return;
          }

          // 先裁剪图片
          final croppedPath = await ImageCropUtil.cropImage(context, imageFile.path);
          if (croppedPath != null) {
            // 检查context是否仍然有效
            if (!context.mounted) {
              debugPrint('Context在图片裁剪后失效');
              return;
            }

            // 显示预览确认对话框
            await CropAvatarDialog.show(
              context,
              imagePath: croppedPath,
              onCropComplete: onCropComplete,
            );
          }
        } else {
          ToastUtils().showToast('image_file_not_exist'.tr);
        }
      }
    } catch (e) {
      debugPrint('相册选择和裁剪失败: $e');
      ToastUtils().showToast('gallery_crop_failed'.tr);
    }
  }

  // 打开相机拍照获取图片
  //
  // [context] 上下文对象
  // [onSelected] 拍照成功后的回调函数
  Future<void> captureFromCamera({
    required BuildContext context,
    required ImagePathCallback onSelected,
  }) async {
    try {
      // 检查context有效性
      if (!context.mounted) {
        debugPrint('Context已失效，无法打开相机');
        return;
      }

      bool hasPermission = await permissionUtil.requestPermissions(context, [Permission.camera]);
      if (!hasPermission) {
        ToastUtils().showToast('camera_permission_denied'.tr);
        return;
      }

      // 检查context是否仍然有效
      if (!context.mounted) {
        debugPrint('Context在权限请求后失效');
        return;
      }

      // 重置为默认前置摄像头状态
      _currentIsFrontCamera = true;

      // 设置摄像头切换监听
      _setupCameraLensChangeListener();

      await CameraPicker.pickFromCamera(
        context,
        createPickerState: () => CustomCameraPickerState(),
        pickerConfig: createCameraConfig(context, (imagePath) async {
          try {
            // 根据当前摄像头类型修正图片方向
            final fixedImagePath = await ImageOrientationUtil.fixImageOrientation(imagePath, isFrontCamera: _currentIsFrontCamera);
            onSelected(fixedImagePath);
          } catch (e) {
            debugPrint('图片方向修正失败: $e');
            // 如果修正失败，使用原图片
            onSelected(imagePath);
          }
        }),
      );
    } catch (e) {
      debugPrint('相机拍照失败: $e');
      ToastUtils().showToast('camera_capture_failed'.tr);
    }
  }

  // 拍照后裁剪并预览确认
  // [onCropComplete] 确认后的回调函数
  Future<void> captureCropAndPreview({
    required BuildContext context,
    required Function(String) onCropComplete,
  }) async {
    try {
      // 检查context有效性
      if (!context.mounted) {
        debugPrint('Context已失效，无法打开相机');
        return;
      }

      bool hasPermission = await permissionUtil.requestPermissions(context, [Permission.camera]);
      if (!hasPermission) {
        ToastUtils().showToast('camera_permission_denied'.tr);
        return;
      }

      // 检查context是否仍然有效
      if (!context.mounted) {
        debugPrint('Context在权限请求后失效');
        return;
      }

      // 重置为默认前置摄像头状态
      _currentIsFrontCamera = true;

      // 设置摄像头切换监听
      _setupCameraLensChangeListener();

      // 使用普通的相机配置，拍照后手动处理裁剪和预览
      await CameraPicker.pickFromCamera(
        context,
        createPickerState: () => CustomCameraPickerState(),
        pickerConfig: createCameraConfig(context, (imagePath) {
          // 延迟执行裁剪和预览流程
          Future.delayed(Duration(milliseconds: 200), () async {
            try {
              // 根据当前摄像头类型修正图片方向
              final fixedImagePath = await ImageOrientationUtil.fixImageOrientation(imagePath, isFrontCamera: _currentIsFrontCamera);

              // 检查context是否仍然有效
              if (Get.context != null && Get.context!.mounted) {
                // 使用修正方向后的图片进行裁剪和预览
                ImageCropUtil.cropAndShowDialog(Get.context!, fixedImagePath, onCropComplete);
              } else {
                debugPrint('Context在图片处理后失效');
                ToastUtils().showToast('camera_crop_failed'.tr);
              }
            } catch (e) {
              debugPrint('图片方向修正失败: $e');
              // 如果修正失败，使用原图片
              if (Get.context != null && Get.context!.mounted) {
                ImageCropUtil.cropAndShowDialog(Get.context!, imagePath, onCropComplete);
              } else {
                debugPrint('Context在错误处理时失效');
                ToastUtils().showToast('camera_crop_failed'.tr);
              }
            }
          });
        }),
      );
    } catch (e) {
      debugPrint('相机拍照和裁剪失败: $e');
      ToastUtils().showToast('camera_crop_failed'.tr);
    }
  }

  // 创建相机配置
  //
  // [context] 上下文对象
  // [onSelected] 拍照成功后的回调函数
  CameraPickerConfig createCameraConfig(BuildContext context, Function(String) onSelected) {
    return CameraPickerConfig(
      enablePinchToZoom: false,
      enableSetExposure: false,
      enableExposureControlOnPoint: false,
      shouldDeletePreviewFile: false,
      preferredFlashMode: FlashMode.off,
      preferredLensDirection: CameraLensDirection.front,
      // 默认使用前置摄像头
      theme: CameraPicker.themeData(Colors.white),
      textDelegate: const CustomCameraPickerTextDelegate(),
      onXFileCaptured: (XFile file, CameraPickerViewType type) {
        // 使用Future.microtask来处理异步回调
        Future.microtask(() async {
          await onSelected(file.path);
        });
        Navigator.of(context).pop();
        return true;
      },
      foregroundBuilder: (BuildContext context, CameraController? controller) {
        // 安全地设置闪光灯模式，避免在控制器被销毁后调用
        if (controller != null && controller.value.isInitialized && !controller.value.isRecordingVideo) {
          try {
            controller.setFlashMode(FlashMode.off);
          } catch (e) {
            debugPrint('⚠️ 设置闪光灯模式失败: $e');
          }
        }
        return const SizedBox();
      },
    );
  }
}
