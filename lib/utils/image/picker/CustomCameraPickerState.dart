import 'dart:async';

import 'package:flutter/material.dart';
import 'package:foreign_friends/theme/ColorSeries.dart';
import 'package:get/get.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';

import '../../../chat/widget/keyboard/camera/PhotoPreviewPage.dart';
import '../../../chat/widget/keyboard/camera/VideoPreviewPage.dart';
import '../ImageOrientationUtil.dart';

// 摄像头切换回调函数类型
typedef CameraLensChangeCallback = void Function(bool isFrontCamera);

// 照片确认回调函数类型
typedef PhotoConfirmCallback = void Function(String photoPath);

// 视频确认回调函数类型
typedef VideoConfirmCallback = void Function(String videoPath, int duration);

// 取消回调函数类型
typedef CancelCallback = void Function();

//相机自定义
class CustomCameraPickerState extends CameraPickerState {
  // 摄像头切换监听回调
  static CameraLensChangeCallback? onCameraLensChanged;

  // 照片确认回调
  static PhotoConfirmCallback? onPhotoConfirm;

  // 视频确认回调
  static VideoConfirmCallback? onVideoConfirm;

  // 取消回调
  static CancelCallback? onCancel;

  // 用于控制 pushToViewer 的 Completer
  Completer<AssetEntity?>? _viewerCompleter;

  // 当前是否为前置摄像头
  bool _isFrontCamera = true;

  // 录制计时器相关
  Timer? _recordingTimer;
  int _recordingSeconds = 0;
  bool _isRecording = false;

  @override
  void initState() {
    super.initState();
    // 初始化时根据配置设置摄像头类型
    _isFrontCamera = pickerConfig.preferredLensDirection == CameraLensDirection.front;

    // 立即通知初始摄像头类型
    if (onCameraLensChanged != null) {
      onCameraLensChanged!(_isFrontCamera);
    }

    // 延迟设置监听器，避免空指针异常
    Future.delayed(Duration(milliseconds: 500), () {
      if (mounted) {
        _setupCameraListener();
      }
    });
  }

  // 设置相机控制器监听
  void _setupCameraListener() {
    try {
      controller.addListener(_onCameraStateChanged);
    } catch (e) {
      // 如果失败，延迟重试
      Future.delayed(Duration(milliseconds: 300), () {
        if (mounted) {
          _setupCameraListener();
        }
      });
    }
  }

  // 相机状态变化回调
  void _onCameraStateChanged() {
    if (!mounted) return;

    try {
      final isCurrentlyRecording = controller.value.isRecordingVideo;
      if (isCurrentlyRecording && !_isRecording) {
        _startRecordingTimer();
      } else if (!isCurrentlyRecording && _isRecording) {
        _stopRecordingTimer();
      }
    } catch (e) {
      // 忽略错误，继续监听
    }
  }

  @override
  void switchCameras() {
    super.switchCameras();

    // 切换摄像头后更新状态
    _isFrontCamera = !_isFrontCamera;

    // 触发回调通知摄像头类型变化
    if (onCameraLensChanged != null) {
      onCameraLensChanged!(_isFrontCamera);
    }
  }

  @override
  Future<AssetEntity?> pushToViewer({required XFile file, required CameraPickerViewType viewType}) {
    // 创建新的 Completer
    _viewerCompleter = Completer<AssetEntity?>();

    // 根据类型显示不同的自定义预览界面
    if (viewType == CameraPickerViewType.image) {
      // 显示自定义照片预览界面
      _showCustomPhotoPreview(file.path);
    } else if (viewType == CameraPickerViewType.video) {
      // 显示自定义视频预览界面
      _showCustomVideoPreview(file.path);
    }

    // 返回 Completer 的 Future
    return _viewerCompleter!.future;
  }

  // 显示自定义照片预览界面
  void _showCustomPhotoPreview(String photoPath) async {
    try {
      // 修正iOS前置摄像头的图片方向
      final fixedPhotoPath = await ImageOrientationUtil.fixImageOrientation(photoPath, isFrontCamera: _isFrontCamera);

      Get.to(
        () => PhotoPreviewPage(
          photoPath: fixedPhotoPath,
          onConfirm: () {
            // 确认使用照片（使用修正后的路径）
            if (onPhotoConfirm != null) {
              onPhotoConfirm!(fixedPhotoPath);
            }
            // 完成 Future 并关闭相机界面
            if (_viewerCompleter != null && !_viewerCompleter!.isCompleted) {
              _viewerCompleter!.complete(null);
            }
            // 主动关闭相机界面
            Navigator.of(context).pop();
          },
          onCancel: () {
            // 取消，完成 Future 但不关闭相机界面
            if (_viewerCompleter != null && !_viewerCompleter!.isCompleted) {
              _viewerCompleter!.complete(null);
            }
          },
        ),
        transition: Transition.cupertino,
      );
    } catch (e) {
      // 如果修正失败，使用原图片
      Get.to(
        () => PhotoPreviewPage(
          photoPath: photoPath,
          onConfirm: () {
            // 确认使用照片（使用原始路径）
            if (onPhotoConfirm != null) {
              onPhotoConfirm!(photoPath);
            }
            // 完成 Future 并关闭相机界面
            if (_viewerCompleter != null && !_viewerCompleter!.isCompleted) {
              _viewerCompleter!.complete(null);
            }
            // 主动关闭相机界面
            Navigator.of(context).pop();
          },
          onCancel: () {
            // 取消，完成 Future 但不关闭相机界面
            if (_viewerCompleter != null && !_viewerCompleter!.isCompleted) {
              _viewerCompleter!.complete(null);
            }
          },
        ),
        transition: Transition.cupertino,
      );
    }
  }

  // 显示自定义视频预览界面
  void _showCustomVideoPreview(String videoPath) {
    Get.to(
      () => VideoPreviewPage(
        videoPath: videoPath,
        duration: 0, // 临时值，在预览页面中获取实际时长
        onConfirm: (String videoPath, int duration) {
          // 确认使用视频
          if (onVideoConfirm != null) {
            onVideoConfirm!(videoPath, duration);
          }
          // 完成 Future 并关闭相机界面
          if (_viewerCompleter != null && !_viewerCompleter!.isCompleted) {
            _viewerCompleter!.complete(null);
          }
          // 主动关闭相机界面
          Navigator.of(context).pop();
        },
        onCancel: () {
          // 取消，完成 Future 但不关闭相机界面
          if (_viewerCompleter != null && !_viewerCompleter!.isCompleted) {
            _viewerCompleter!.complete(null);
          }
        },
      ),
      transition: Transition.cupertino,
    );
  }

  // 获取当前摄像头类型
  bool get isFrontCamera => _isFrontCamera;

  // 重写build方法来添加自定义计时器
  @override
  Widget build(BuildContext context) {
    final originalWidget = super.build(context);
    return Stack(
      children: [
        originalWidget,
        // 添加自定义计时器显示
        if (_isRecording) _buildRecordingTimer(context),
      ],
    );
  }

  // 开始录制计时器
  void _startRecordingTimer() {
    _isRecording = true;
    _recordingSeconds = 0;
    _recordingTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _recordingSeconds++;
        });
      }
    });
  }

  // 停止录制计时器
  void _stopRecordingTimer({bool updateUI = true}) {
    _isRecording = false;
    _recordingTimer?.cancel();
    _recordingTimer = null;
    if (mounted && updateUI) {
      setState(() {
        _recordingSeconds = 0;
      });
    } else {
      // 如果不需要更新UI，直接重置状态
      _recordingSeconds = 0;
    }
  }

  // 格式化录制时间显示
  String _formatRecordingTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  // 构建录制计时器显示
  Widget _buildRecordingTimer(BuildContext context) {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 15,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.6),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 录制指示点
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: ColorSeries.emphasize,
                  shape: BoxShape.circle,
                ),
              ),
              SizedBox(width: 8),
              // 计时器文本

              Text(
                _formatRecordingTime(_recordingSeconds),
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  fontFeatures: [FontFeature.tabularFigures()],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    // 在dispose时停止计时器，但不更新UI
    _stopRecordingTimer(updateUI: false);

    // 移除监听器
    try {
      controller.removeListener(_onCameraStateChanged);
    } catch (e) {
      // 忽略错误
    }

    super.dispose();
  }
}
