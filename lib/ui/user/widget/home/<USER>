import 'package:flutter/material.dart';
import 'package:foreign_friends/theme/AvatarImage.dart';
import 'package:foreign_friends/theme/ColorNatural.dart';
import 'package:foreign_friends/theme/NameText.dart';
import 'package:foreign_friends/ui/user/vm/UserVM.dart';
import 'package:foreign_friends/utils/AppUtils.dart';
import 'package:foreign_friends/utils/extensionApp.dart';
import 'package:foreign_friends/utils/image/ImageLoading.dart';
import 'package:foreign_friends/utils/image/ImagePath.dart';
import 'package:get/get.dart';

// 用户主页顶部信息展示组件
class UserHomeTopWidget extends StatelessWidget {
  final UserVM vm;

  const UserHomeTopWidget({super.key, required this.vm});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // VIP专属图片，只有VIP用户才可见
        Positioned(
          top: 120,
          left: 15,
          right: 10,
          child: Visibility(
            visible: vm.model.myPageDataBean?.data?.user?.isVip == 1,
            child: Stack(
              children: [
                ImageLoading(
                  imageUrl: ImagePath.path("ic_user_home_7.png"),
                  width: double.maxFinite,
                  height: 90,
                  fit: BoxFit.fill,
                ),
                Positioned(
                  right: 2,
                  top: 40,
                  child: Transform.rotate(
                    angle: 6 * 3.14159 / 180,
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text("vip_update_period".tr,
                              style: TextStyle(
                                  fontSize: 11,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold)),
                          Text(
                              "vip_expire_date".tr.replaceAll(
                                  "%date%",
                                  vm.model.myPageDataBean?.data?.user
                                          ?.vipText ??
                                      ""),
                              style: TextStyle(
                                  fontSize: 11,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold))
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        // 顶部背景和内容容器

        Container(
          margin: EdgeInsets.only(left: 8, right: 8, top: 8),
          width: double.maxFinite,
          height: 150,
          decoration: BoxDecoration(
            image: DecorationImage(
                image: AssetImage(ImagePath.path("ic_user_home_1.png")),
                fit: BoxFit.cover),
            borderRadius: BorderRadius.circular(15),
          ),
          child: Stack(
            children: [
              // Logo或VIP标识图标，根据用户VIP状态显示不同图标
              Positioned(
                top: 30, // 调整Logo位置
                left: 0,
                right: 0,
                child: ImageLoading(
                  imageUrl: ImagePath.path(
                      vm.model.myPageDataBean?.data?.user?.isVip == 1
                          ? "ic_user_home_6.png"
                          : "ic_user_home_2.png"),
                  width: double.maxFinite,
                  height: 20,
                  fit: BoxFit.cover,
                ),
              ),

              // 用户头像和个人信息区域
              Positioned(
                left: 20,
                top: 80, // 调整用户信息位置
                child: Row(
                  children: [
                    // 用户头像，带VIP标识
                    AvatarImage(
                      imageUrl: vm.model.myPageDataBean?.data?.user?.head ?? "",
                      width: 45,
                      height: 45,
                      fit: BoxFit.cover,
                      borderColor: Colors.white,
                      borderWidth: 1,
                      isVip: vm.model.myPageDataBean?.data?.user?.isVip == 1,
                    ),
                    SizedBox(width: 10),
                    // 用户基本信息区域
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 用户昵称，VIP用户显示特殊样式
                        NameText(
                          text: vm.model.myPageDataBean?.data?.user?.nick ??
                              "...",
                          isVip:
                              vm.model.myPageDataBean?.data?.user?.isVip == 1,
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 15,
                              fontWeight: FontWeight.bold),
                        ).marginOnly(left: 3),
                        SizedBox(height: 5),
                        // 用户详细信息：性别、年龄和地址
                        Row(
                          children: [
                            // 性别图标 - 性别为0时不显示
                            if (vm.model.myPageDataBean?.data?.user?.sex !=
                                    null &&
                                vm.model.myPageDataBean!.data!.user!.sex !=
                                    0) ...[
                              Icon(
                                vm.model.myPageDataBean!.data!.user!.sex == 1
                                    ? Icons.male
                                    : Icons.female,
                                color:
                                    vm.model.myPageDataBean!.data!.user!.sex ==
                                            1
                                        ? Colors.blue
                                        : Colors.pink,
                                size: 16,
                              ),
                              SizedBox(width: 4),
                            ],
                            SizedBox(width: 5),
                            // 用户年龄
                            Text(
                              "${AppUtils.calculateAge(vm.model.myPageDataBean?.data?.user?.birthday ?? "")}${"age_unit".tr}",
                              style: TextStyle(
                                  color: ColorNatural.natural_4, fontSize: 12),
                            ),
                            // 分隔符
                            Text("•",
                                style: TextStyle(
                                    color: ColorNatural.natural_4,
                                    fontSize: 12)),
                            // 用户地址
                            Text(
                              vm.model.myPageDataBean?.data?.user?.address ??
                                  "...",
                              style: TextStyle(
                                  color: ColorNatural.natural_4, fontSize: 12),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // 右侧功能图标
              Positioned(
                right: 15,
                top: 90,
                child: ImageLoading(
                  imageUrl: ImagePath.path("ic_user_home_5.png"),
                  width: 22,
                  height: 22,
                  fit: BoxFit.fill,
                ),
              ),

              // 右上角功能按钮组
              Positioned(
                right: 10,
                top: 20,
                child: Row(
                  children: [
                    // 第一个功能按钮
                    ImageLoading(
                      imageUrl: ImagePath.path("ic_user_home_4.png"),
                      width: 44,
                      height: 44,
                      fit: BoxFit.cover,
                    ).onTap(() {
                      Get.toNamed("/EditUserInfoPage");
                    }),
                    // 第二个功能按钮
                    ImageLoading(
                      imageUrl: ImagePath.path("ic_user_home_3.png"),
                      width: 44,
                      height: 44,
                      fit: BoxFit.cover,
                    ).onTap(() {
                      Get.toNamed("/SettingPage");
                    }),
                  ],
                ),
              ),
            ],
          ),
        ).onTap(() {
          Get.toNamed("/EditUserInfoPage");
        }),

        // 提供足够的空间给VIP专属图片
        vm.model.myPageDataBean?.data?.user?.isVip == 1
            ? SizedBox(height: 210)
            : SizedBox(height: 155), // 调整整体高度
      ],
    );
  }
}
