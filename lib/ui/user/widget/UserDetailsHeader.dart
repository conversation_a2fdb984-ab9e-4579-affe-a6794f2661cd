import 'package:flutter/material.dart';
import 'package:foreign_friends/storage/UserManage.dart';
import 'package:foreign_friends/theme/AvatarImage.dart';
import 'package:foreign_friends/theme/ColorNatural.dart';
import 'package:foreign_friends/theme/NameText.dart';
import 'package:foreign_friends/utils/image/ImageLoading.dart';
import 'package:foreign_friends/utils/image/ImagePath.dart';
import 'package:get/get.dart';

import '../../../chat/dialog/ChatUserActionDialog.dart';
import '../bean/UserDetailsBean.dart';

// 用户详情页顶部组件
class UserDetailsHeader extends StatelessWidget {
  final UserDetailsBean? userDetails;
  final VoidCallback? onEditPressed;
  final VoidCallback? onBlockUser;
  final VoidCallback? onReportUser;

  const UserDetailsHeader({
    super.key,
    required this.userDetails,
    this.onEditPressed,
    this.onBlockUser,
    this.onReportUser,
  });

  @override
  Widget build(BuildContext context) {
    // 如果用户数据为空，显示占位UI
    if (userDetails == null) {
      return const SizedBox();
    }
    // 计算状态栏的高度
    final double statusBarHeight = MediaQuery.of(context).padding.top;
    return Stack(
      children: [
        // 主要内容区域
        Padding(
          padding: EdgeInsets.only(top: statusBarHeight + 10),
          child: Column(
            mainAxisSize: MainAxisSize.min, // 让Column自适应内容高度
            children: [
              // 用户头像
              AvatarImage(
                imageUrl: userDetails?.head ?? '',
                width: 60,
                height: 60,
                fit: BoxFit.cover,
                borderPadding: 2,
                borderWidth: 3,
                isVip: userDetails?.isVip == 1,
              ),
              SizedBox(height: 10),

              // 用户名
              NameText(
                style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: ColorNatural.natural_1),
                text: userDetails?.nick ?? '',
                isVip: userDetails?.isVip == 1,
              ),
              SizedBox(height: 4),

              // 用户信息行 (性别、年龄、地址)
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 性别图标 - 性别为0时不显示
                  if (userDetails?.sex != null && userDetails!.sex != 0) ...[
                    Icon(
                      userDetails!.sex == 1 ? Icons.male : Icons.female,
                      color: userDetails!.sex == 1 ? Colors.blue : Colors.pink,
                      size: 16,
                    ),
                    SizedBox(width: 4),
                  ],
                  // 年龄信息
                  Text(
                    '・${calculateAge(userDetails?.birthday) ?? '?'}歳',
                    style:
                        TextStyle(fontSize: 12, color: ColorNatural.natural_4),
                  ),
                  SizedBox(width: 4),
                  // 地址信息
                  Text(
                    '・${userDetails?.address ?? ''}',
                    style:
                        TextStyle(fontSize: 12, color: ColorNatural.natural_4),
                  ),
                ],
              ),
              SizedBox(height: 16),

              // 标签行
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                    children: userDetails!.tags!
                        .map((tag) => buildTagChip(tag))
                        .toList()),
              ),
              SizedBox(height: 16),
            ],
          ),
        ),

        // 顶部按钮栏
        Positioned(
          top: statusBarHeight + 10,
          left: 16,
          right: 16,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 返回按钮
              GestureDetector(
                onTap: () => Get.back(),
                child: ImageLoading(
                  imageUrl: ImagePath.path("ic_user_details_1.png"),
                  width: 30,
                  height: 30,
                  fit: BoxFit.fill,
                ),
              ),

              // 右侧按钮 - 根据是否是当前用户显示编辑或更多按钮
              GestureDetector(
                onTap: userDetails!.id == UserManage.instance.getUserId()
                    ? onEditPressed
                    : () => onMoreButtonTap(),
                child: ImageLoading(
                  imageUrl: ImagePath.path(
                      userDetails!.id == UserManage.instance.getUserId()
                          ? "ic_user_details_2.png" // 编辑按钮
                          : "ic_dating_details_2.png" // 更多按钮
                      ),
                  width: 30,
                  height: 30,
                  fit: BoxFit.fill,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 更多按钮点击事件
  void onMoreButtonTap() {
    if (userDetails?.id != null) {
      ChatUserActionDialog.show(
        context: Get.context!,
        userId: userDetails!.id.toString(),
        userName: userDetails!.nick,
        isBlocked: userDetails!.isBlack ?? false,
        onBlock: () => onBlockUser?.call(),
        onReport: () => onReportUser?.call(),
      );
    }
  }

  // 构建单个标签组件
  Widget buildTagChip(String label) {
    return Padding(
      padding: EdgeInsets.only(right: 8),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        decoration: BoxDecoration(
          color: ColorNatural.natural_4.withOpacity(0.2),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(label,
            style: TextStyle(fontSize: 12, color: ColorNatural.natural_4)),
      ),
    );
  }

  // 从生日计算年龄
  String? calculateAge(String? birthday) {
    if (birthday == null || birthday.isEmpty) {
      return null;
    }

    try {
      final birthDate = DateTime.parse(birthday);
      final today = DateTime.now();
      var age = today.year - birthDate.year;
      if (today.month < birthDate.month ||
          (today.month == birthDate.month && today.day < birthDate.day)) {
        age--;
      }
      return age.toString();
    } catch (e) {
      return null;
    }
  }
}
