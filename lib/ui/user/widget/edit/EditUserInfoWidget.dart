import 'package:flutter/material.dart';
import 'package:foreign_friends/storage/UserManage.dart';
import 'package:foreign_friends/theme/AvatarImage.dart';
import 'package:foreign_friends/theme/ColorSeries.dart';
import 'package:foreign_friends/utils/AppUtils.dart';
import 'package:foreign_friends/utils/extensionApp.dart';
import 'package:foreign_friends/utils/image/ImagePath.dart';
import 'package:foreign_friends/utils/validator/NicknameValidator.dart';
import 'package:get/get.dart';

import '../../../login/widget/AddressSelector.dart';
import '../../../login/widget/BirthdaySelector.dart';
import '../../../login/widget/GenderSelector.dart';
import '../../../login/widget/ReCustomTextField.dart';
import '../../vm/EditUserInfoVM.dart';

class EditUserInfoWidget extends StatelessWidget {
  final EditUserInfoVM vm;

  const EditUserInfoWidget({super.key, required this.vm});

  @override
  Widget build(BuildContext context) {
    var userInfo = vm.model.editUserInfoPageData!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        // 编辑内容
        Stack(
          children: [
            // 内容容器
            Column(
              children: [
                Container(
                  margin: EdgeInsets.all(10),
                  alignment: Alignment.center,
                  width: double.maxFinite,
                  decoration: BoxDecoration(
                    image: DecorationImage(image: AssetImage(ImagePath.path("ic_register_bg.png")), fit: BoxFit.fill),
                  ),
                  child: Column(
                    children: [
                      // 性别选择
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          // 左侧占位，为头像留出空间，使用屏幕宽度的比例
                          SizedBox(width: MediaQuery.of(context).size.width * 0.28),
                          // 性别选择器

                          Expanded(
                            child: GenderSelector(
                              initialGender: userInfo.sex == 1 ? Gender.male : (userInfo.sex == 2 ? Gender.female : null),
                              selectable: false,
                              iconSize: 24,
                              margin: EdgeInsets.only(top: MediaQuery.of(context).size.height * 0.019, right: 15, left: 20),
                              iconContainerSize: 30,
                              optionHeight: MediaQuery.of(context).size.height * 0.045,
                              borderRadius: 12,
                              titleBottomSpacing: MediaQuery.of(context).size.height * 0.01,
                              title: 'gender_title'.tr,
                              titleIconSize: 18,
                              titleStyle: TextStyle(color: ColorSeries.assist, fontSize: 12, fontWeight: FontWeight.w600),
                              titleIconPath: ImagePath.path("ic_re_gender.png"),
                              unselectedBackgroundColor: const Color(0xFF617980).withOpacity(0.12),
                              maleIconPath: ImagePath.path("ic_male_icon.png"),
                              femaleIconPath: ImagePath.path("ic_female_icon.png"),
                              onGenderChanged: (gender) {
                                userInfo.sex = gender == Gender.male ? 1 : 2;
                                vm.update();
                              },
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: MediaQuery.of(context).size.height * 0.032),
                      // 昵称输入框
                      ReCustomTextField(
                        initialValue: userInfo.nick,
                        label: 'nickname_title'.tr,
                        hint: 'nickname_hint'.tr,
                        maxLength: 12,
                        iconPath: ImagePath.path("ic_re_number.png"),
                        backgroundColor: const Color(0xFF617980).withOpacity(0.12),
                        borderRadius: 12,
                        labelStyle: TextStyle(color: ColorSeries.assist, fontSize: 12, fontWeight: FontWeight.w600),
                        margin: EdgeInsets.only(left: 20, right: 20, top: MediaQuery.of(context).size.height * 0.015),
                        height: 45,
                        hintStyle: TextStyle(color: ColorSeries.assist, fontSize: 14, fontWeight: FontWeight.bold),
                        textStyle: TextStyle(color: ColorSeries.assist, fontSize: 14, fontWeight: FontWeight.bold),
                        // 添加输入格式化器，过滤emoji和不允许的特殊符号
                        inputFormatters: [NicknameInputFormatter()],
                        onChanged: (value) {
                          userInfo.nick = value;
                          vm.setUserName(value);
                        },
                      ),

                      // 日期选择
                      BirthdaySelector(
                        selectedBirthday: AppUtils.calculateAge(userInfo.birthday).toString(),
                        onTap: () => {vm.showBirthdayDialog()},
                      ),

                      // 地址选择
                      AddressSelector(
                        selectedAddress: userInfo.address ?? "",
                        onTap: () => {vm.showAddressDialog()},
                      ),
                      SizedBox(height: MediaQuery.of(context).size.height * 0.022),
                    ],
                  ),
                ),
              ],
            ).marginOnly(left: MediaQuery.of(context).size.width * 0.012, top: MediaQuery.of(context).size.height * 0.003),

            // 头像 - 使用相对尺寸适配不同屏幕
            Positioned(
              left: 15 + (MediaQuery.of(context).size.width > 400 ? 5 : 0),
              top: MediaQuery.of(context).size.height < 700 ? 12 : (MediaQuery.of(context).size.height < 850 ? 22 : 20),
              child: AvatarImage(
                width: MediaQuery.of(context).size.width * 0.19,
                height: MediaQuery.of(context).size.width * 0.19,
                borderColor: ColorSeries.light_1,
                borderWidth: 3,
                imageUrl: userInfo.head ?? "",
                isVip: UserManage.instance.isVip(),
                fit: BoxFit.cover,
              ).onTap(() {
                vm.showImageSelectionDialog();
              }),
            ),
          ],
        ),
      ],
    );
  }
}
