import 'package:foreign_friends/base/BaseModel.dart';
import 'package:foreign_friends/http/ApiServe.dart';
import 'package:foreign_friends/ui/login/utils/VerificationCodeTimer.dart';
import 'package:foreign_friends/utils/LogUtils.dart';
import 'package:foreign_friends/utils/ToastUtils.dart';
import 'package:get/get.dart';

// 绑定邮箱页面Model
class BindEmailModel extends BaseModel {
  // 邮箱地址
  String email = '';
  bool isEmailValid = false;

  // 验证码
  String code = '';
  bool isCodeValid = false;
  bool isCountingDown = false;

  // 是否可以绑定
  bool canBind = false;

  // 倒计时器
  final VerificationCodeTimer timer = VerificationCodeTimer(timeoutSeconds: 60);

  // 发送验证码API
  void sendCodeApi() {
    sendApi(
      showLoading: true,
      url: ApiServe.sendChangeCode,
      jsonBody: {"email": email},
      dataCall: (data) {
        timer.startTimer();
        isCountingDown = true;
        ToastUtils().showToast('bind_email_code_sent'.tr);
        refresh();
      },
      errorCall: (msg, code) {
        ToastUtils().showToast(msg);
      },
    );
  }

  // 绑定邮箱API
  void bindEmailApi() {
    sendApi(
      showLoading: true,
      url: ApiServe.setBindEmail,
      jsonBody: {
        "email": email,
        "code": code,
      },
      dataCall: (data) {
        Get.back();
        ToastUtils().showToast('bind_email_success'.tr);
      },
      errorCall: (msg, code) {
        LogUtils.e("绑定邮箱API请求失败: $msg, code: $code");
        ToastUtils().showToast(msg);
      },
    );
  }
}
