import 'dart:io';

import 'package:flutter/material.dart';

import '../../../base/BaseModel.dart';
import '../../../http/ApiServe.dart';
import '../../../http/HttpConfig.dart';
import '../../../utils/LogUtils.dart';
import '../bean/VersionCheckBean.dart';
import '../dialog/NetworkErrorDialog.dart';
import '../dialog/UpdateVersionDialog.dart';

class SplashModel extends BaseModel {
  // 版本检查接口
  void checkVersionApi({required Function(VersionCheckBean) onSuccess, required Function(String, int) onError}) {
    // 获取平台类型 0苹果 1谷歌或安卓
    int platform = Platform.isIOS ? 0 : 1;
    sendApi(
      url: ApiServe.commonVersion,
      jsonBody: {'platform': platform},
      dataCall: (data) {
        VersionCheckBean versionBean = VersionCheckBean.fromJson(data);
        if (versionBean.isSuccess) {
          HttpConfig.isInAudit = versionBean.data!.isInAudit;
          onSuccess(versionBean);
        } else {
          onError(versionBean.msg ?? '版本检查失败', versionBean.status ?? -1);
        }
      },
      errorCall: (error, code) {
        onError(error, code);
      },
    );
  }

  // 处理版本检查结果并显示更新对话框
  void handleVersionCheck(BuildContext context, {required Function() onContinue}) {
    // 检查BuildContext是否仍然有效
    if (!context.mounted) {
      LogUtils.e('BuildContext已经被销毁，无法进行版本检查');
      onContinue();
      return;
    }

    checkVersionApi(
      onSuccess: (versionBean) {
        // 再次检查BuildContext是否仍然有效
        if (!context.mounted) {
          LogUtils.e('版本检查成功但BuildContext已经被销毁');
          onContinue();
          return;
        }

        // 检查是否需要显示更新
        if (versionBean.data != null && versionBean.data!.shouldShowUpdate) {
          // 显示版本更新对话框
          UpdateVersionDialog(
            versionData: versionBean.data!,
            onCancel: () {
              // 非强制更新时，用户点击取消继续下一步
              onContinue();
            },
            onForceUpdate: () {
              // 强制更新时，不执行任何后续代码，等待用户操作
              LogUtils.i('强制更新：等待用户操作');
            },
          ).showVersionDialog(context);
        } else {
          // 不需要更新，直接继续下一步
          onContinue();
        }
      },
      onError: (error, code) {
        // 版本检查失败，显示网络错误弹窗
        LogUtils.e('版本检查失败: $error, 错误码: $code');
        // 检查BuildContext是否仍然有效再显示错误弹窗
        if (context.mounted) {
          showNetworkErrorDialog(context, onContinue);
        } else {
          LogUtils.e('版本检查失败但BuildContext已经被销毁，直接继续');
          onContinue();
        }
      },
    );
  }

  // 显示网络错误弹窗
  void showNetworkErrorDialog(BuildContext context, Function() onContinue) {
    // 检查BuildContext是否仍然有效
    if (!context.mounted) {
      LogUtils.e('BuildContext已经被销毁，无法显示网络错误弹窗');
      // 如果context无效，直接执行继续逻辑
      onContinue();
      return;
    }

    showDialog(
      context: context,
      barrierDismissible: false, // 不允许点击外部关闭
      builder: (BuildContext dialogContext) {
        return PopScope(
          canPop: false, // 不允许返回键关闭
          child: NetworkErrorDialog(
            onRetry: () {
              // 检查对话框context是否仍然有效
              if (dialogContext.mounted) {
                Navigator.of(dialogContext).pop(); // 关闭弹窗
              }
              // 检查原始context是否仍然有效再进行重试
              if (context.mounted) {
                // 点击重试，重新进行版本检查
                handleVersionCheck(context, onContinue: onContinue);
              } else {
                LogUtils.e('原始BuildContext已经被销毁，无法重试版本检查');
                onContinue();
              }
            },
          ),
        );
      },
    );
  }
}
