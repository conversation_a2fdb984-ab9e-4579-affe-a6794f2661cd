import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../http/HttpConfig.dart';
import '../../user/dialog/WebDesDialog.dart';
import '../vm/LoginVM.dart';

// 用户协议弹窗
class UserAgreementDialog {
  // 显示用户协议弹窗
  static void show({
    required BuildContext context,
    required LoginVM viewModel,
    VoidCallback? onAgree,
  }) {
    showDialog(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.black.withValues(alpha: 0.6),
      builder: (BuildContext context) {
        return UserAgreementDialogWidget(
          viewModel: viewModel,
          onAgree: onAgree,
        );
      },
    );
  }
}

// 用户协议弹窗组件
class UserAgreementDialogWidget extends StatelessWidget {
  final LoginVM viewModel;
  final VoidCallback? onAgree;

  const UserAgreementDialogWidget({
    super.key,
    required this.viewModel,
    this.onAgree,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        margin: const EdgeInsets.all(40),
        decoration: BoxDecoration(
          color: const Color(0xFF1D1D1F),
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.5),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 10),

            // 标题
            Text(
              "login_agreement_dialog_title".tr,
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 16),

            // 协议内容
            Flexible(
              child: SingleChildScrollView(
                child: buildAgreementContent(),
              ),
            ),

            const SizedBox(height: 20),

            // 按钮区域
            Row(
              children: [
                // 取消按钮
                Expanded(
                  child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      alignment: Alignment.center,
                      height: 45,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                            color: Colors.white.withValues(alpha: 0.3)),
                        color: Colors.transparent,
                      ),
                      child: Text(
                        "login_agreement_dialog_cancel".tr,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 12),

                // 已阅读同意按钮
                Expanded(
                  child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      // 设置为已同意状态
                      viewModel.isAgreementAccepted = true;
                      viewModel.update();
                      Navigator.of(context).pop();

                      // 执行同意后的回调
                      if (onAgree != null) {
                        onAgree!();
                      }
                    },
                    child: Container(
                      alignment: Alignment.center,
                      height: 45,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: Colors.white,
                      ),
                      child: Text(
                        "login_agreement_dialog_agree".tr,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.black,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  // 构建协议内容
  Widget buildAgreementContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 提示文本
        Text(
          "login_agreement_dialog_content".tr,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.9),
            fontSize: 14,
            fontWeight: FontWeight.w400,
            height: 1.4,
          ),
        ),
        SizedBox(height: 16),

        // 协议链接
        Text.rich(
          TextSpan(
            children: [
              TextSpan(
                text: "login_terms_agreement_prefix".tr,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white.withValues(alpha: 0.8),
                  height: 1.4,
                ),
              ),
              TextSpan(
                text: "login_terms_agreement_link".tr,
                style: TextStyle(
                  fontSize: 14,
                  color: const Color(0xFFF5CA5C),
                  height: 1.4,
                  decoration: TextDecoration.underline,
                ),
                recognizer: TapGestureRecognizer()
                  ..onTap = () => onTermsOfServiceTap(),
              ),
              TextSpan(
                text: "login_terms_agreement_middle".tr,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white.withValues(alpha: 0.8),
                  height: 1.4,
                ),
              ),
              TextSpan(
                text: "login_privacy_policy_link".tr,
                style: TextStyle(
                  fontSize: 14,
                  color: const Color(0xFFF5CA5C),
                  height: 1.4,
                  decoration: TextDecoration.underline,
                ),
                recognizer: TapGestureRecognizer()
                  ..onTap = () => onPrivacyPolicyTap(),
              ),
              TextSpan(
                text: "login_terms_agreement_suffix".tr,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white.withValues(alpha: 0.8),
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 点击利用规约
  void onTermsOfServiceTap() {
    if (Get.context != null) {
      WebDesDialog.show(Get.context!,
          title: 'other_terms_of_service'.tr,
          url: HttpConfig.termsOfServiceUrl);
    }
  }

  // 点击隐私政策
  void onPrivacyPolicyTap() {
    if (Get.context != null) {
      WebDesDialog.show(Get.context!,
          title: 'other_privacy_policy'.tr, url: HttpConfig.privacyPolicyUrl);
    }
  }
}
