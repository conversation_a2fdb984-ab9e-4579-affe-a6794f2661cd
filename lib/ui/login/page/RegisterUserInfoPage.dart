import 'package:flutter/material.dart';
import 'package:foreign_friends/base/BasePage.dart';
import 'package:foreign_friends/theme/ColorNatural.dart';
import 'package:foreign_friends/theme/ColorSeries.dart';
import 'package:foreign_friends/utils/image/ImageLoading.dart';
import 'package:foreign_friends/utils/image/ImagePath.dart';
import 'package:foreign_friends/utils/widget/KeyboardAwareContainer.dart';
import 'package:get/get.dart';

import '../../../theme/AppBackground.dart';
import '../../../utils/AppUtils.dart';
import '../../../utils/validator/NicknameValidator.dart';
import '../vm/RegisterUserInfoVM.dart';
import '../widget/AddressSelector.dart';
import '../widget/AnimatedIconButton.dart';
import '../widget/AvatarUploadWidget.dart';
import '../widget/BirthdaySelector.dart';
import '../widget/FadeInWidget.dart';
import '../widget/GenderSelector.dart';
import '../widget/ReCustomTextField.dart';

//注册
class RegisterUserInfoPage extends BasePage<RegisterUserInfoVM> {
  const RegisterUserInfoPage({super.key});

  @override
  bool isShowStatusBar() {
    return false;
  }

  @override
  Widget buildView(BuildContext context, RegisterUserInfoVM vm) {
    // 创建一个页面内容组件
    Widget pageContent = Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        KeyboardAnimatedContainer(
          normalPadding: EdgeInsets.only(top: (50 + AppUtils.getHigh(context))),
          keyboardPadding: EdgeInsets.only(top: 50),
          child: Container(),
        ),

        // 标题文本
        FadeInWidget(
          delay: const Duration(milliseconds: 300),
          duration: const Duration(milliseconds: 800),
          child: Container(
            margin: EdgeInsets.only(left: 20, right: 20, top: 0, bottom: 27),
            child: Text(
              "register_step1_title".tr,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 24,
                height: 1.5,
                color: ColorSeries.light_1,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),

        // 注册内容
        Stack(
          children: [
            // 内容容器
            Column(
              children: [
                FadeInWidget(
                  delay: const Duration(milliseconds: 800),
                  duration: const Duration(milliseconds: 800),
                  child: Container(
                    width: double.maxFinite,
                    margin: EdgeInsets.only(left: 8, right: 8),
                    decoration: BoxDecoration(
                      image: DecorationImage(image: AssetImage(ImagePath.path("ic_register_bg.png")), fit: BoxFit.fill),
                    ),
                    child: Column(
                      children: [
                        // 性别选择
                        FadeInWidget(
                          delay: const Duration(milliseconds: 1000),
                          duration: const Duration(milliseconds: 800),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              // 左侧占位，为头像留出空间，使用屏幕宽度的比例
                              SizedBox(width: MediaQuery.of(context).size.width * 0.28),
                              // 性别选择器使用 Expanded 自适应剩余空间

                              Expanded(
                                child: GenderSelector(
                                  iconSize: 24,
                                  margin: EdgeInsets.only(top: MediaQuery.of(context).size.height * 0.019, right: 15, left: 20),
                                  iconContainerSize: 30,
                                  optionHeight: MediaQuery.of(context).size.height * 0.045,
                                  borderRadius: 12,
                                  titleBottomSpacing: MediaQuery.of(context).size.height * 0.01,
                                  title: 'gender_title'.tr,
                                  titleIconSize: 18,
                                  titleStyle: TextStyle(color: ColorSeries.assist, fontSize: 12, fontWeight: FontWeight.w600),
                                  titleIconPath: ImagePath.path("ic_re_gender.png"),
                                  unselectedBackgroundColor: const Color(0xFF617980).withOpacity(0.12),
                                  maleIconPath: ImagePath.path("ic_male_icon.png"),
                                  femaleIconPath: ImagePath.path("ic_female_icon.png"),
                                  onGenderChanged: (gender) {
                                    vm.model.body.sex = gender == Gender.male ? "1" : "2";
                                    vm.upStepTip();
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),

                        Row(
                          children: [
                            SizedBox(width: MediaQuery.of(context).size.width * 0.32),
                            Expanded(
                              child: Text(
                                "gender_selection_warning".tr,
                                textAlign: TextAlign.left,
                                style: TextStyle(fontWeight: FontWeight.w600, color: ColorSeries.assist, fontSize: 11),
                              ),
                            ),
                            SizedBox(width: MediaQuery.of(context).size.width * 0.05),
                          ],
                        ),

                        // 昵称输入框
                        FadeInWidget(
                          delay: const Duration(milliseconds: 1200),
                          duration: const Duration(milliseconds: 800),
                          child: ReCustomTextField(
                            label: 'nickname_title'.tr,
                            hint: 'nickname_hint'.tr,
                            focusNode: vm.inputFocusNode,
                            maxLength: 12,
                            iconPath: ImagePath.path("ic_re_number.png"),
                            backgroundColor: const Color(0xFF617980).withOpacity(0.12),
                            borderRadius: 12,
                            labelStyle: TextStyle(color: ColorSeries.assist, fontSize: 12, fontWeight: FontWeight.w600),
                            margin: EdgeInsets.only(left: 20, right: 20, top: MediaQuery.of(context).size.height * 0.015),
                            height: 45,
                            hintStyle: TextStyle(color: ColorSeries.assist, fontSize: 14, fontWeight: FontWeight.w600),
                            textStyle: TextStyle(color: ColorSeries.assist, fontSize: 14, fontWeight: FontWeight.w600),
                            // 添加输入格式化器，过滤emoji和不允许的特殊符号
                            inputFormatters: [NicknameInputFormatter()],
                            onChanged: (value) {
                              // 使用新的验证方法
                              vm.validateNicknameInput(value);
                            },
                          ),
                        ),

                        // 日期选择
                        FadeInWidget(
                          delay: const Duration(milliseconds: 1400),
                          duration: const Duration(milliseconds: 800),
                          skipAnimation: vm.model.body.birthday.isNotEmpty,
                          child: BirthdaySelector(
                            selectedBirthday: vm.model.ageText,
                            onTap: () => {vm.showBirthdayDialog()},
                          ),
                        ),

                        // 地址选择
                        FadeInWidget(
                          delay: const Duration(milliseconds: 1600),
                          duration: const Duration(milliseconds: 800),
                          skipAnimation: vm.model.body.address.isNotEmpty,
                          child: AddressSelector(
                            selectedAddress: vm.model.body.address,
                            onTap: () => {vm.showAddressDialog()},
                          ),
                        ),
                        SizedBox(height: MediaQuery.of(context).size.height * 0.022),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 10),

                // 提示标语
                Visibility(
                  visible: vm.model.userTipsGone,
                  child: FadeInWidget(
                    delay: const Duration(milliseconds: 1700),
                    duration: const Duration(milliseconds: 800),
                    child: Text(
                      vm.model.userTips,
                      style: TextStyle(
                        fontSize: 12,
                        color: ColorNatural.natural_1,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),

                // 底部按钮
                FadeInWidget(
                  delay: const Duration(milliseconds: 1000),
                  duration: const Duration(milliseconds: 800),
                  child: Container(
                    margin: EdgeInsets.only(left: 30, right: 30, top: 30),
                    child: Row(
                      children: [
                        // 上一步按钮
                        GestureDetector(
                          onTap: () => {Get.back()},
                          child: ImageLoading(imageUrl: ImagePath.path("ic_previous.png"), width: 66, height: 66, fit: BoxFit.fill),
                        ),
                        const Expanded(child: SizedBox.shrink()),

                        AnimatedIconButton(
                          activeIcon: Image.asset(ImagePath.path("ic_next.png"), key: const ValueKey("active"), width: 66, height: 66),
                          inactiveIcon: Image.asset(ImagePath.path("ic_next_true.png"), key: const ValueKey("inactive"), width: 66, height: 66),
                          isActive: vm.model.userTipsGone,
                          onTap: () => {vm.nextClick()},
                          duration: const Duration(milliseconds: 300),
                        )
                      ],
                    ),
                  ),
                )
              ],
            ).marginOnly(left: MediaQuery.of(context).size.width * 0.012, top: MediaQuery.of(context).size.height * 0.003),

            // 头像 - 使用相对尺寸适配不同屏幕
            Obx(() => Positioned(
                  left: 13 + (MediaQuery.of(context).size.width > 400 ? 5 : 0),
                  top: MediaQuery.of(context).size.height < 700 ? 12 : (MediaQuery.of(context).size.height < 850 ? 16 : 9),
                  child: FadeInWidget(
                    delay: const Duration(milliseconds: 600),
                    duration: const Duration(milliseconds: 800),
                    skipAnimation: vm.model.userHead.value.isNotEmpty,
                    child: AvatarUploadWidget(
                      width: vm.model.userHead.value.isNotEmpty ? MediaQuery.of(context).size.width * 0.18 : MediaQuery.of(context).size.width * 0.21,
                      height: vm.model.userHead.value.isNotEmpty ? MediaQuery.of(context).size.width * 0.18 : MediaQuery.of(context).size.width * 0.21,
                      backgroundColor: vm.model.userHead.value.isNotEmpty ? Colors.black : Colors.transparent,
                      borderColor: ColorSeries.light_1,
                      borderWidth: 3,
                      iconPath: vm.model.userHead.value.isEmpty ? ImagePath.path("ic_re_upload.png") : vm.model.userHead.value,
                      iconSize: 32,
                      iconColor: Colors.white,
                      text: 'avatar_title'.tr,
                      textStyle: const TextStyle(color: Colors.white, fontSize: 8, fontWeight: FontWeight.w300),
                      onTap: () {
                        vm.showImageSelectionDialog();
                      },
                    ),
                  ),
                )),
          ],
        ),
        // 添加底部间距以避免键盘遮挡
        SizedBox(height: 120),
      ],
    );

    // 使用KeyboardAwareContainer包装页面内容并添加AppBackground
    return WillPopScope(
      onWillPop: () async {
        Navigator.of(context).pop();
        return false;
      },
      child: Stack(
        children: [
          // 背景动画，确保完全覆盖屏幕
          const Positioned.fill(child: AppBackground()),

          // 页面内容
          KeyboardAwareContainer(
            bottomPadding: 120,
            child: pageContent,
          ),
        ],
      ),
    );
  }

  @override
  RegisterUserInfoVM createViewModel() {
    return RegisterUserInfoVM();
  }

  @override
  void onDispose() {}

  @override
  void onInit(BuildContext context, RegisterUserInfoVM vm) {}

  @override
  void onViewCreated(BuildContext context, RegisterUserInfoVM vm) {}
}
