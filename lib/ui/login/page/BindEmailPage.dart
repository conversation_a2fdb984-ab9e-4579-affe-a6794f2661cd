import 'package:flutter/material.dart';

import '../../../base/BasePage.dart';
import '../../../theme/AppBackground.dart';
import '../vm/BindEmailVM.dart';
import '../widget/BindEmailButtonWidget.dart';
import '../widget/BindEmailFormWidget.dart';
import '../widget/BindEmailHeaderWidget.dart';

// 绑定邮箱页面
class BindEmailPage extends BasePage<BindEmailVM> {
  const BindEmailPage({super.key});

  @override
  bool isShowStatusBar() {
    return false;
  }

  @override
  BindEmailVM createViewModel() {
    return BindEmailVM();
  }

  @override
  void onInit(BuildContext context, BindEmailVM vm) {
    // 初始化操作
  }

  @override
  void onViewCreated(BuildContext context, BindEmailVM vm) {
    // 视图创建完成后的操作
  }

  @override
  void onDispose() {
    // 页面销毁时的清理操作
  }

  @override
  Widget buildView(BuildContext context, BindEmailVM vm) {
    return Stack(
      children: [
        // 背景
        AppBackground(),

        // 内容
        SafeArea(
          child: Scaffold(
            backgroundColor: Colors.transparent,
            body: Column(
              children: [
                // 头部组件
                BindEmailHeaderWidget(),

                // 主要内容区域
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 40),

                        // 邮箱输入表单
                        BindEmailFormWidget(vm: vm),

                        SizedBox(height: 40),

                        // 绑定按钮
                        BindEmailButtonWidget(vm: vm),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
