import 'package:flutter/material.dart';
import 'package:foreign_friends/theme/ColorNatural.dart';
import 'package:get/get.dart';

import '../vm/BindEmailVM.dart';
import '../widget/FadeInWidget.dart';

// 绑定邮箱按钮组件
class BindEmailButtonWidget extends StatelessWidget {
  final BindEmailVM vm;

  const BindEmailButtonWidget({
    super.key,
    required this.vm,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BindEmailVM>(
      builder: (vm) {
        return FadeInWidget(
          delay: Duration(milliseconds: 600),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 24),
            child: SizedBox(
              width: double.infinity,
              height: 55,
              child: ElevatedButton(
                onPressed: vm.model.canBind
                    ? () {
                        vm.bindEmail();
                      }
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: vm.model.canBind
                      ? ColorNatural.natural_1
                      : ColorNatural.natural_12,
                  foregroundColor:
                      vm.model.canBind ? Colors.black : ColorNatural.natural_6,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                ),
                child: Text(
                  'bind_email_bind_button'.tr,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
