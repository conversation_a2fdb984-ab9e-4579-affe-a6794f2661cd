import 'package:flutter/material.dart';
import 'package:foreign_friends/theme/ColorNatural.dart';
import 'package:foreign_friends/ui/login/widget/CountdownButton.dart';
import 'package:foreign_friends/ui/login/widget/EmailInputField.dart';
import 'package:foreign_friends/utils/image/ImageLoading.dart';
import 'package:foreign_friends/utils/image/ImagePath.dart';
import 'package:get/get.dart';

import '../vm/BindEmailVM.dart';
import '../widget/FadeInWidget.dart';

// 绑定邮箱表单组件
class BindEmailFormWidget extends StatelessWidget {
  final BindEmailVM vm;

  const BindEmailFormWidget({
    super.key,
    required this.vm,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BindEmailVM>(
      builder: (vm) {
        return FadeInWidget(
          delay: Duration(milliseconds: 400),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 邮箱地址标题
                Text(
                  'bind_email_address'.tr,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: ColorNatural.natural_1,
                  ),
                ),

                SizedBox(height: 15),

                // 邮箱地址输入框
                EmailInputField(
                  height: 55,
                  focusNode: vm.emailFocusNode,
                  backgroundColor: ColorNatural.natural_15,
                  controller: vm.emailController,
                  borderRadius: 10,
                  margin: EdgeInsets.zero,
                  showClearButton: false,
                  borderColor: ColorNatural.natural_12,
                  borderWidth: 1.0,
                  textStyle: TextStyle(
                    fontSize: 16.0,
                    color: ColorNatural.natural_1,
                    fontWeight: FontWeight.w600,
                  ),
                  hintText: 'bind_email_address_hint'.tr,
                  onChanged: (text) => vm.onEmailChanged(text),
                ),

                SizedBox(height: 15),

                // 验证码输入行
                Row(
                  children: [
                    Expanded(
                      child: EmailInputField(
                        height: 55,
                        focusNode: vm.codeFocusNode,
                        backgroundColor: ColorNatural.natural_15,
                        controller: vm.codeController,
                        margin: EdgeInsets.zero,
                        borderRadius: 10,
                        borderColor: ColorNatural.natural_12,
                        borderWidth: 1.0,
                        hintText: 'bind_email_code'.tr,
                        onChanged: (text) => vm.onCodeChanged(text),
                        textStyle: TextStyle(
                          fontSize: 16.0,
                          color: ColorNatural.natural_1,
                          fontWeight: FontWeight.w600,
                        ),
                        sixDigitsOnly: true,
                        showClearButton: false,
                      ),
                    ),

                    SizedBox(width: 10),

                    // 发送验证码按钮
                    ValueListenableBuilder<int>(
                      valueListenable: vm.model.timer.remainingSeconds,
                      builder: (context, seconds, child) {
                        return CountdownButton(
                          icon: ImageLoading(
                            imageUrl: ImagePath.path("ic_fluent_ems.png"),
                            width: 24,
                            height: 24,
                            fit: BoxFit.fill,
                          ),
                          width: 80,
                          height: 55,
                          borderRadius: 15,
                          iconColor: vm.model.isEmailValid
                              ? ColorNatural.natural_1
                              : ColorNatural.natural_12,
                          countdownColor: ColorNatural.natural_12,
                          text: vm.model.isCountingDown ? '${seconds}s' : '',
                          onPressed: vm.model.isEmailValid && !vm.model.isCountingDown
                              ? () {
                                  vm.sendCode();
                                }
                              : null,
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
