import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../http/HttpConfig.dart';
import '../../../theme/ColorNatural.dart';
import '../../user/dialog/WebDesDialog.dart';
import '../vm/LoginVM.dart';

// 用户协议组件
class UserAgreementWidget extends StatelessWidget {
  final LoginVM viewModel;

  const UserAgreementWidget({
    super.key,
    required this.viewModel,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(left: 32, right: 32, top: 24),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 选中按钮
          GestureDetector(
            onTap: () => viewModel.toggleAgreement(),
            child: Container(
              width: 16,
              height: 16,
              margin: EdgeInsets.only(top: 2, right: 8),
              decoration: BoxDecoration(
                border: Border.all(
                  color: viewModel.isAgreementAccepted ? ColorNatural.natural_15 : ColorNatural.natural_2,
                  width: 1.5,
                ),
                borderRadius: BorderRadius.circular(3),
                color: viewModel.isAgreementAccepted ? ColorNatural.natural_15 : Colors.transparent,
              ),
              child: viewModel.isAgreementAccepted
                  ? Icon(
                      Icons.check,
                      size: 12,
                      color: Colors.white,
                    )
                  : null,
            ),
          ),

          // 协议文本
          Expanded(
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: "login_terms_agreement_prefix".tr,
                    style: TextStyle(
                      fontSize: 12,
                      color: ColorNatural.natural_2,
                      height: 1.4,
                    ),
                  ),
                  TextSpan(
                    text: "login_terms_agreement_link".tr,
                    style: TextStyle(
                      fontSize: 12,
                      color: const Color(0xFFF5CA5C),
                      height: 1.4,
                    ),
                    recognizer: TapGestureRecognizer()..onTap = () => onTermsOfServiceTap(),
                  ),
                  TextSpan(
                    text: "login_terms_agreement_middle".tr,
                    style: TextStyle(
                      fontSize: 12,
                      color: ColorNatural.natural_2,
                      height: 1.4,
                    ),
                  ),
                  TextSpan(
                    text: "login_privacy_policy_link".tr,
                    style: TextStyle(
                      fontSize: 12,
                      color: const Color(0xFFF5CA5C),
                      height: 1.4,
                    ),
                    recognizer: TapGestureRecognizer()..onTap = () => onPrivacyPolicyTap(),
                  ),
                  TextSpan(
                    text: "login_terms_agreement_suffix".tr,
                    style: TextStyle(
                      fontSize: 12,
                      color: ColorNatural.natural_2,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 点击利用规约
  void onTermsOfServiceTap() {
    if (Get.context != null) {
      WebDesDialog.show(Get.context!, title: 'other_terms_of_service'.tr, url: HttpConfig.termsOfServiceUrl);
    }
  }

  // 点击隐私政策
  void onPrivacyPolicyTap() {
    if (Get.context != null) {
      WebDesDialog.show(Get.context!, title: 'other_privacy_policy'.tr, url: HttpConfig.privacyPolicyUrl);
    }
  }
}
