import 'package:flutter/material.dart';
import 'package:foreign_friends/theme/ColorNatural.dart';
import 'package:get/get.dart';

import '../../../utils/image/ImagePath.dart';

// 绑定邮箱页面头部组件
class BindEmailHeaderWidget extends StatelessWidget {
  const BindEmailHeaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 顶部导航栏
        _buildTopBar(),
      ],
    );
  }

  // 构建顶部导航栏
  Widget _buildTopBar() {
    return Container(
      height: 60,
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          // 返回按钮
          GestureDetector(
            onTap: () => {Get.back()},
            child: Padding(
              padding: EdgeInsets.only(left: 6, right: 16, top: 6, bottom: 6),
              child: Image.asset(ImagePath.path("ic_bai_back.png"), width: 24, height: 24),
            ),
          ),

          Expanded(
            child: Center(
              child: Text(
                'bind_email_title'.tr,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: ColorNatural.natural_1,
                ),
              ),
            ),
          ),

          // 占位，保持标题居中
          SizedBox(width: 40),
        ],
      ),
    );
  }
}
