import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:foreign_friends/base/BaseModel.dart';
import 'package:foreign_friends/base/BaseViewModel.dart';
import 'package:foreign_friends/http/HttpConfig.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../theme/ColorNatural.dart';
import '../../../utils/image/picker/ImagePickerSingle.dart';
import '../../../utils/picker/DatePickerUtils.dart';
import '../../../utils/picker/JapanRegionPicker.dart';
import '../../../utils/validator/NicknameValidator.dart';
import '../dialog/ImageSelectionDialog.dart';
import '../model/RegisterUserInfoModel.dart';

class RegisterUserInfoVM extends BaseViewModel {
  FocusNode inputFocusNode = FocusNode();
  ImagePickerSingle imagePickerSingle = ImagePickerSingle();
  RegisterUserInfoModel model = RegisterUserInfoModel();

  @override
  List<BaseModel> bindModels() {
    return [model];
  }

  @override
  void initData() {}

  //选择生日
  void showBirthdayDialog() {
    DatePickerUtils.showBirthdayPicker(
      context: Get.context!,
      onResult: (result) {
        model.ageText = result.age.toString();
        model.body.birthday = DateFormat('yyyy-MM-dd').format(result.date);
        upStepTip();
      },
    );
  }

  //点击下一步
  void nextClick() {
    if (!model.userTipsGone) {
      // 如果处于审核状态，设置性别为0
      if (HttpConfig.isInAudit) {
        model.body.sex = "0";
      }
      model.sendUserInfoApi();
    }
  }

  // 验证昵称输入
  void validateNicknameInput(String value) {
    // 验证昵称
    String? validationError = NicknameValidator.validateNickname(value);
    if (validationError != null) {
      model.userTipsGone = true;
      model.userTips = validationError;
      update();
      return;
    }

    // 如果验证通过，更新昵称并继续正常流程
    model.body.nick = value;
    upStepTip(isInput: true);
  }

  //更新数据和底部步骤提示
  void upStepTip({bool isInput = false}) {
    // 审核状态下跳过性别检查
    if (!HttpConfig.isInAudit && model.body.sex.isEmpty) {
      model.userTipsGone = true;
      model.userTips = "user_tips_select_gender".tr;
    } else if (model.userHead.value.isEmpty) {
      model.userTipsGone = true;
      model.userTips = "user_tips_select_avatar".tr;
    } else if (model.body.nick.isEmpty) {
      model.userTipsGone = true;
      model.userTips = "user_tips_select_name".tr;
    } else if (model.body.birthday.isEmpty) {
      model.userTipsGone = true;
      model.userTips = "user_tips_select_age".tr;
    } else if (model.body.address.isEmpty) {
      model.userTipsGone = true;
      model.userTips = "user_tips_select_address".tr;
    } else {
      model.userTips = "user_tips_all_completed".tr;
      model.userTipsGone = false;
    }

    if (!isInput) {
      inputFocusNode.unfocus();
    }
    update();
  }

  //选择地址
  void showAddressDialog() {
    JapanRegionPicker.showRegionPicker(
      context: Get.context!,
      backgroundColor: ColorNatural.natural_16,
      textColor: Colors.white,
      themeColor: Colors.white,
      onResult: (JapanRegionResult result) {
        model.body.address = result.regionName;
        upStepTip();
      },
    );
  }

  //选择头像图片
  void showImageSelectionDialog() {
    ImageSelectionDialog.showActionSheet(
      Get.context!,
      onGalleryTap: () {
        //相册选择、裁剪并预览确认
        imagePickerSingle.selectCropAndPreview(
          context: Get.context!,
          onCropComplete: (croppedPath) {
            model.userHead.value = croppedPath;
            upStepTip();
          },
        );
      },
      onCameraTap: () {
        //相机拍照、裁剪并预览确认
        imagePickerSingle.captureCropAndPreview(
          context: Get.context!,
          onCropComplete: (croppedPath) {
            model.userHead.value = croppedPath;
            upStepTip();
          },
        );
      },
    );
  }

  @override
  void onClose() {
    inputFocusNode.dispose();
    super.onClose();
  }
}
