import 'package:flutter/material.dart';
import 'package:foreign_friends/base/BaseModel.dart';
import 'package:foreign_friends/base/BaseViewModel.dart';
import 'package:foreign_friends/utils/AppUtils.dart';
import 'package:foreign_friends/utils/ToastUtils.dart';
import 'package:get/get.dart';

import '../model/BindEmailModel.dart';

// 绑定邮箱页面ViewModel
class BindEmailVM extends BaseViewModel {
  BindEmailModel model = BindEmailModel();

  // 邮箱地址输入控制器
  TextEditingController emailController = TextEditingController();
  FocusNode emailFocusNode = FocusNode();

  // 验证码输入控制器
  TextEditingController codeController = TextEditingController();
  FocusNode codeFocusNode = FocusNode();

  @override
  List<BaseModel> bindModels() {
    return [model];
  }

  @override
  void initData() {
    // 设置倒计时器回调
    model.timer.onTimerComplete = () {
      onCountdownFinished();
    };
  }

  // 邮箱地址输入监听
  void onEmailChanged(String email) {
    model.email = email;
    model.isEmailValid = AppUtils.isValidEmail(email);
    checkCanBind();
    update();
  }

  // 验证码输入监听
  void onCodeChanged(String code) {
    model.code = code;
    model.isCodeValid = code.length >= 6;
    checkCanBind();
    update();
  }

  // 检查是否可以绑定
  void checkCanBind() {
    model.canBind = model.isEmailValid && model.isCodeValid;
  }

  // 发送验证码
  void sendCode() {
    if (model.isCountingDown || !model.isEmailValid) return;

    // 取消邮箱输入框焦点
    emailFocusNode.unfocus();

    model.sendCodeApi();
  }

  // 倒计时结束
  void onCountdownFinished() {
    model.isCountingDown = false;
    update();
  }

  // 绑定邮箱
  void bindEmail() {
    // 验证邮箱地址
    if (model.email.isEmpty) {
      ToastUtils().showToast('bind_email_please_input_email'.tr);
      return;
    }

    if (!model.isEmailValid) {
      ToastUtils().showToast('bind_email_invalid_email'.tr);
      return;
    }

    // 验证验证码
    if (model.code.isEmpty) {
      ToastUtils().showToast('bind_email_please_input_code'.tr);
      return;
    }

    if (!model.isCodeValid) {
      ToastUtils().showToast('bind_email_invalid_code'.tr);
      return;
    }

    // 取消焦点
    emailFocusNode.unfocus();
    codeFocusNode.unfocus();

    // 调用绑定接口
    model.bindEmailApi();
  }

  @override
  void onClose() {
    emailController.dispose();
    emailFocusNode.dispose();
    codeController.dispose();
    codeFocusNode.dispose();
    model.timer.dispose();
    super.onClose();
  }
}
