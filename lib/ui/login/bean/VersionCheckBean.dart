// 版本检查响应数据模型
class VersionCheckBean {
  int? status;
  VersionCheckData? data;
  String? msg;

  VersionCheckBean({this.status, this.data, this.msg});

  VersionCheckBean.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? VersionCheckData.fromJson(json['data']) : null;
    msg = json['msg'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['msg'] = msg;
    return data;
  }

  // 判断请求是否成功
  bool get isSuccess => status == 0;
}

// 版本检查数据
class VersionCheckData {
  int? isShowUpdate; // 是否显示更新信息 1是0否
  VersionInfo? info; // 显示的更新信息

  VersionCheckData({this.isShowUpdate, this.info});

  VersionCheckData.fromJson(Map<String, dynamic> json) {
    isShowUpdate = json['is_show_update'];
    info = json['info'] != null ? VersionInfo.fromJson(json['info']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['is_show_update'] = isShowUpdate;
    if (info != null) {
      data['info'] = info!.toJson();
    }
    return data;
  }

  // 是否需要显示更新
  bool get shouldShowUpdate => isShowUpdate == 1;
}

// 版本信息
class VersionInfo {
  String? des; // 版本描述
  String? url; // 版本下载地址
  String? version; // 最新版本号
  int? isForce; // 是否强制更新 1是0否

  VersionInfo({this.des, this.url, this.version, this.isForce});

  VersionInfo.fromJson(Map<String, dynamic> json) {
    des = json['des'];
    url = json['url'];
    version = json['version'];
    isForce = json['is_force'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['des'] = des;
    data['url'] = url;
    data['version'] = version;
    data['is_force'] = isForce;
    return data;
  }

  // 是否强制更新
  bool get isForceUpdate => isForce == 1;
}
