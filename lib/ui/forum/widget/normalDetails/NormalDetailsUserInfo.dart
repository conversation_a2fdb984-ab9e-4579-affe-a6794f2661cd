import 'package:flutter/material.dart';
import 'package:foreign_friends/theme/AvatarImage.dart';
import 'package:foreign_friends/theme/ColorNatural.dart';
import 'package:foreign_friends/theme/NameText.dart';
import 'package:foreign_friends/utils/AppUtils.dart';
import 'package:foreign_friends/utils/extensionApp.dart';
import 'package:foreign_friends/utils/image/ImageLoading.dart';
import 'package:foreign_friends/utils/image/ImagePath.dart';
import 'package:get/get.dart';

import '../../vm/NormalDetailsVM.dart';

// 普通帖子详情 - 用户信息
class NormalDetailsUserInfo extends StatelessWidget {
  final NormalAddDetailsVM vm;

  const NormalDetailsUserInfo({super.key, required this.vm});

  @override
  Widget build(BuildContext context) {
    final user = vm.model.details?.user;

    if (user == null) {
      return const SizedBox.shrink();
    }

    // 年龄
    var age = "";
    if (user.birthday != null) {
      age = "${AppUtils.calculateAge(user.birthday!)}${'age_unit'.tr}";
    }
    // 地址
    var address = "";
    if (user.address != null) {
      address = "・${user.address!}";
    }

    return Expanded(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
        decoration: BoxDecoration(
            color: ColorNatural.natural_1,
            borderRadius: BorderRadius.circular(22)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            // 头像
            AvatarImage(
              imageUrl: user.head ?? "",
              width: 25,
              height: 25,
              fit: BoxFit.cover,
              isVip: user.isVip == 1,
            ).onTap(() {
              Get.toNamed('/UserDetailsPage',
                  arguments: {'userId': user.id.toString()});
            }),
            SizedBox(width: 5),
            // 用户信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 昵称
                  NameText(
                    text: user.nick ?? "",
                    style: TextStyle(
                        color: ColorNatural.natural_18,
                        fontSize: 10,
                        fontWeight: FontWeight.w600),
                    isVip: user.isVip == 1,
                  ).marginOnly(left: 3),
                  // 年龄 地址
                  Row(
                    children: [
                      // 性别图标 - 性别为0时不显示
                      if (user.sex != 0) ...[
                        Icon(user.sex == 1 ? Icons.male : Icons.female,
                            color: ColorNatural.natural_6, size: 14),
                        SizedBox(width: 4),
                      ],
                      Text('$age$address',
                          style: TextStyle(
                              color: ColorNatural.natural_10,
                              fontSize: 9,
                              fontWeight: FontWeight.w300))
                    ],
                  )
                ],
              ),
            ),
            SizedBox(width: 8),
            // 更多按钮
            GestureDetector(
              onTap: () => {vm.userInfoMoreClick(user)},
              child: ImageLoading(
                imageUrl: ImagePath.path("ic_normal_details_1.png"),
                width: 32,
                height: 32,
                fit: BoxFit.fill,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
