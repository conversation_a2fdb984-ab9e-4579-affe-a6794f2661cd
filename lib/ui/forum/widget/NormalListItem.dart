import 'package:flutter/material.dart';
import 'package:foreign_friends/theme/AvatarImage.dart';
import 'package:foreign_friends/theme/ColorNatural.dart';
import 'package:foreign_friends/theme/NameText.dart';
import 'package:foreign_friends/ui/forum/model/NormalListBean.dart';
import 'package:foreign_friends/utils/AppUtils.dart';
import 'package:foreign_friends/ui/forum/dialog/NormalDetailsMoreDialog.dart';
import 'package:foreign_friends/ui/forum/dialog/report/ReportDialog.dart';
import 'package:foreign_friends/utils/ToastUtils.dart';
import 'package:foreign_friends/http/ApiServe.dart';
import 'package:foreign_friends/http/AppHttpRequest.dart';
import 'package:get/get.dart';

import 'normalListItem/ImageGridWidget.dart';
import 'normalListItem/ListItemFooter.dart';
import 'normalListItem/SimpleVideoPreviewWidget.dart';

// 普通帖子列表的入口组件（调度器）
// 该组件统一处理所有普通帖子（纯文本、图文、视频）的布局，确保样式一致。
class NormalListItem extends StatefulWidget {
  final NormalItemBean item;
  final Function(UserBean)? onAvatarTap; // 头像点击回调
  final Function(NormalItemBean)? onLikeTap; // 点赞点击回调
  final Function(NormalItemBean)? onItemTap; // Item整体点击回调
  final Function(NormalItemBean)? onDeleteTap; // 删除帖子回调
  final Function(NormalItemBean)? onReportTap; // 举报帖子回调

  const NormalListItem({
    super.key,
    required this.item,
    this.onAvatarTap,
    this.onLikeTap,
    this.onItemTap,
    this.onDeleteTap,
    this.onReportTap,
  });

  @override
  State<NormalListItem> createState() => NormalListItemState();
}

class NormalListItemState extends State<NormalListItem>
    with AutomaticKeepAliveClientMixin {
  // 状态保存 - 保持Widget状态，避免重建
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用super.build以支持AutomaticKeepAliveClientMixin
    var user = widget.item.user;
    // 基础布局为水平排列的Row，左侧是头像，右侧是帖子内容。
    // 添加整体点击事件
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        // 点击整体Item跳转到普通帖子详情
        widget.onItemTap?.call(widget.item);
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 头像
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () => widget.onAvatarTap?.call(user!), // 绑定头像点击事件
            child: AvatarImage(
                imageUrl: user?.head ?? '',
                width: 40,
                height: 40,
                fit: BoxFit.cover,
                isVip: user?.isVip == 1),
          ),
          SizedBox(width: 12),
          // 右侧内容区域，使用Expanded占据剩余空间
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 统一的头部信息（用户名、性别、年龄等）
                buildHeader(user),
                SizedBox(height: 10),
                // 动态内容区域（根据帖子类型显示文本、图片或视频）
                buildContent(),

                SizedBox(height: 15),
                // 统一的尾部（时间、地址、评论、点赞）
                ListItemFooter(item: widget.item, onLikeTap: widget.onLikeTap),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建统一的头部组件
  Widget buildHeader(UserBean? user) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 左侧内容区域
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 帖子标题，如果标题为空则显示用户昵称
              NameText(
                text: widget.item.title ?? user?.nick ?? "",
                isVip: user?.isVip == 1,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: ColorNatural.natural_1,
                ),
              ).marginOnly(left: 2),
              SizedBox(height: 2),
              // 用户基本信息（性别、年龄）
              Row(
                children: [
                  // 性别图标 - 性别为0时不显示
                  if (user?.sex != null && user!.sex != 0) ...[
                    Icon(user!.sex == 1 ? Icons.male : Icons.female,
                        color: user!.sex == 1 ? Colors.blue : Colors.pink[200],
                        size: 16),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 4),
                      child: Text("•",
                          style:
                              TextStyle(fontSize: 12, color: Colors.white70)),
                    ),
                  ],
                  Text(
                    "${AppUtils.calculateAge(user?.birthday)}${'age_unit'.tr}",
                    style: TextStyle(fontSize: 12, color: Colors.white70),
                  ),
                ],
              ),
            ],
          ),
        ),
        // 右侧更多按钮
        buildMoreButton(user),
      ],
    );
  }

  // 根据帖子类型构建动态内容组件
  Widget buildContent() {
    final bool hasVideo =
        widget.item.video != null && widget.item.video!.isNotEmpty;
    final bool hasImages =
        widget.item.picList != null && widget.item.picList!.isNotEmpty;

    // 帖子正文（所有类型的帖子都可能包含文字内容）
    final contentText =
        (widget.item.content != null && widget.item.content!.isNotEmpty)
            ? Padding(
                padding: EdgeInsets.only(bottom: 10),
                child: Text(
                  widget.item.content!,
                  style: TextStyle(
                      fontSize: 14, color: Colors.white.withValues(alpha: 0.9)),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              )
            : SizedBox.shrink();

    Widget specificContent;
    if (hasVideo) {
      // 如果有视频
      specificContent = ConstrainedBox(
        constraints: BoxConstraints(maxHeight: 300, maxWidth: double.infinity),
        child: SimpleVideoPreviewWidget(
            videoUrl: widget.item.video, videoTime: widget.item.videoTime),
      );
    } else if (hasImages) {
      // 如果有图片，则显示图片网格
      specificContent = ImageGridWidget(images: widget.item.picList ?? []);
    } else {
      // 对于纯文本帖子，其内容已在contentText中处理完毕，此处直接返回即可
      return contentText;
    }

    // 对于图文或视频帖子，将文字内容和特定媒体内容组合在一个Column中显示
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min, // 添加这个约束
      children: [contentText, specificContent],
    );
  }

  // 构建右上角更多按钮
  Widget buildMoreButton(UserBean? user) {
    if (user == null) return SizedBox.shrink();

    return GestureDetector(
      onTap: () => _showMoreDialog(user),
      child: Container(
        padding: EdgeInsets.all(4),
        child: Icon(
          Icons.more_horiz,
          color: Colors.white70,
          size: 20,
        ),
      ),
    );
  }

  // 显示更多操作对话框
  void _showMoreDialog(UserBean user) {
    NormalDetailsMoreDialog.show(
      context: context,
      userBean: user,
      onDelete: () => _deletePost(),
      onReport: () => _reportPost(),
    );
  }

  // 删除帖子
  void _deletePost() {
    if (widget.item.id == null) return;

    // 调用删除API
    AppHttpRequest().sendApi(
      url: ApiServe.postingsDel,
      body: {"id": widget.item.id},
      showLoading: true,
      dataCall: (data) {
        ToastUtils().showToast("post_delete_success".tr);
        // 调用外部删除回调
        widget.onDeleteTap?.call(widget.item);
      },
      errorCall: (error, code) {
        // 错误处理
      },
    );
  }

  // 举报帖子
  void _reportPost() {
    if (widget.item.id == null) return;

    ReportDialog.show(
      context,
      targetId: widget.item.id!,
      reportType: 1, // 1表示普通贴
      onConfirm: () {
        // 调用外部举报回调
        widget.onReportTap?.call(widget.item);
      },
    );
  }
}
