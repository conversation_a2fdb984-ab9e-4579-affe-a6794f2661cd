import 'package:flutter/material.dart';
import 'package:foreign_friends/utils/PermissionUtil.dart';
import 'package:foreign_friends/utils/ToastUtils.dart';
import 'package:foreign_friends/utils/image/picker/AssetPickerConfigManager.dart';
import 'package:foreign_friends/utils/video/VideoFilterManager.dart';
import 'package:foreign_friends/utils/crash/CrashManager.dart';
import 'package:get/get.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

import '../../vm/NormalAddVM.dart';

// 普通帖子相册选择器
class NormalAddAlbumSelector {
  final PermissionUtil permissionUtil = PermissionUtil();

  // 从相册选择媒体（图片或视频）
  Future<void> selectMedia({
    required BuildContext context,
    required NormalAddVM vm,
  }) async {
    // 请求相册权限
    bool hasPermission = await permissionUtil.requestPhotoPermission(context);
    if (!hasPermission) {
      ToastUtils().showToast('photo_permission_denied'.tr);
      return;
    }

    try {
      // 创建选择器配置
      final pickerConfig =
          AssetPickerConfigManager.createMediaPickerConfig(context);

      // 使用 AssetPicker 选择媒体
      final List<AssetEntity>? result = await AssetPicker.pickAssets(
        context,
        pickerConfig: pickerConfig,
      );

      // 处理选择结果
      if (result != null && result.isNotEmpty) {
        await handleSelectedMedia(result, vm);
      }
    } catch (e) {
      ToastUtils().showToast('select_media_failed'.tr);
      debugPrint('选择媒体失败: $e');
    }
  }

  // 从相册选择照片
  Future<void> selectPhotos({
    required BuildContext context,
    required NormalAddVM vm,
  }) async {
    // 请求相册权限
    bool hasPermission = await permissionUtil.requestPhotoPermission(context);
    if (!hasPermission) {
      ToastUtils().showToast('photo_permission_denied'.tr);
      return;
    }

    try {
      // 创建照片选择器配置
      final pickerConfig = AssetPickerConfigManager.createPhotosPickerConfig(
        context,
        selectedAssets: vm.selectedImages,
      );

      // 使用 AssetPicker 选择照片
      final List<AssetEntity>? result = await AssetPicker.pickAssets(
        context,
        pickerConfig: pickerConfig,
      );

      // 处理选择结果
      if (result != null) {
        vm.setImages(result);
        debugPrint('选择照片成功: ${result.length}张');
      }
    } catch (e) {
      ToastUtils().showToast('select_media_failed'.tr);
      debugPrint('选择照片失败: $e');
    }
  }

  // 从相册选择视频
  Future<void> selectVideo({
    required BuildContext context,
    required NormalAddVM vm,
  }) async {
    // 请求相册权限
    bool hasPermission = await permissionUtil.requestPhotoPermission(context);
    if (!hasPermission) {
      ToastUtils().showToast('photo_permission_denied'.tr);
      return;
    }

    try {
      // 创建视频选择器配置
      final pickerConfig =
          AssetPickerConfigManager.createVideoPickerConfig(context);

      // 使用 AssetPicker 选择视频
      final List<AssetEntity>? result = await AssetPicker.pickAssets(
        context,
        pickerConfig: pickerConfig,
      );

      // 处理选择结果
      if (result != null && result.isNotEmpty) {
        // 使用VideoFilterManager筛选视频大小
        final filteredVideos =
            await VideoFilterManager.instance.filterVideos(result);
        if (filteredVideos != null && filteredVideos.isNotEmpty) {
          vm.setVideo(filteredVideos.first);
          debugPrint('选择视频成功');
        }
      }
    } catch (e) {
      ToastUtils().showToast('select_media_failed'.tr);
      debugPrint('选择视频失败: $e');
    }
  }

  // 处理选中的媒体
  Future<void> handleSelectedMedia(
      List<AssetEntity> assets, NormalAddVM vm) async {
    try {
      if (assets.isEmpty) return;

      // 检查是否混合选择了图片和视频
      final hasImages = assets.any((asset) => asset.type == AssetType.image);
      final hasVideos = assets.any((asset) => asset.type == AssetType.video);

      if (hasImages && hasVideos) {
        ToastUtils().showToast('cannot_select_both_image_and_video'.tr);
        return;
      }

      if (hasVideos) {
        // 如果选择了视频，只取第一个并检查大小限制
        final videoAsset =
            assets.firstWhere((asset) => asset.type == AssetType.video);
        // 使用VideoFilterManager筛选视频大小
        final filteredVideos =
            await VideoFilterManager.instance.filterVideos([videoAsset]);
        if (filteredVideos != null && filteredVideos.isNotEmpty) {
          vm.setVideo(filteredVideos.first);
          debugPrint('选择视频成功');
        }
      } else if (hasImages) {
        // 如果选择了图片，设置所有图片
        final imageAssets =
            assets.where((asset) => asset.type == AssetType.image).toList();
        vm.setImages(imageAssets);
        debugPrint('选择图片成功: ${imageAssets.length}张');
      }
    } catch (e) {
      ToastUtils().showToast('process_media_failed'.tr);
      debugPrint('处理媒体失败: $e');
    }
  }
}
