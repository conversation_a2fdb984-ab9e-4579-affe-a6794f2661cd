import 'package:flutter/material.dart';
import 'package:foreign_friends/ui/square/vm/RecommendVM.dart';
import 'package:foreign_friends/utils/image/ImageLoading.dart';
import 'package:foreign_friends/utils/image/ImagePath.dart';
import 'package:get/get.dart';

import '../../../theme/AvatarImage.dart';
import '../../../theme/ColorNatural.dart';
import '../../../theme/NameText.dart';
import '../bean/HomeWaterfallBean.dart';

class BuildUserCardItemWidget extends StatefulWidget {
  final HomeWaterfallUser itemData;
  final RecommendVM vm;
  final int index;

  const BuildUserCardItemWidget(
      {super.key,
      required this.itemData,
      required this.vm,
      required this.index});

  @override
  State<BuildUserCardItemWidget> createState() =>
      _BuildUserCardItemWidgetState();
}

class _BuildUserCardItemWidgetState extends State<BuildUserCardItemWidget> {
  // 根据索引获取项目高度
  double getItemHeight() {
    if (widget.index % 2 == 0) {
      // 左列
      return (widget.index % 4 == 0) ? 250 : 200;
    } else {
      // 右列
      return (widget.index % 4 == 1) ? 200 : 250;
    }
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          Get.toNamed('/UserDetailsPage',
              arguments: {'userId': widget.itemData.id.toString()});
        },
        child: Container(
          height: getItemHeight(),
          decoration: BoxDecoration(
            color: ColorNatural.natural_15,
            borderRadius: BorderRadius.circular(10),
            // 添加阴影效果
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 图片区域
              Expanded(
                child: Stack(
                  children: [
                    // 主图片
                    Container(
                      margin: EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      width: double.infinity,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: ImageLoading(
                          key: ValueKey('image_${widget.itemData.normalImg}'),
                          imageUrl: widget.itemData.normalImg,
                          width: double.infinity,
                          height: double.infinity,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // 用户信息区域
              Padding(
                padding:
                    EdgeInsets.only(left: 12, right: 12, top: 4, bottom: 10),
                child: Row(
                  children: [
                    // 头像
                    AvatarImage(
                      key: ValueKey('avatar_${widget.itemData.head}'),
                      imageUrl: widget.itemData.head,
                      width: 25,
                      height: 25,
                      fit: BoxFit.cover,
                      isVip: widget.itemData.isVip == 1,
                    ),

                    SizedBox(width: 8),

                    // 用户信息
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          NameText(
                            isVip: widget.itemData.isVip == 1,
                            style: TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.w600,
                              color: ColorNatural.natural_4,
                            ),
                            text: widget.itemData.nick,
                          ),
                          SizedBox(height: 2),
                          Row(
                            children: [
                              // 性别图标 - 性别为0时不显示
                              if (widget.itemData.sex != 0) ...[
                                ImageLoading(
                                  imageUrl: widget.itemData.sex == 1
                                      ? ImagePath.path("ic_sq_nan.png")
                                      : ImagePath.path("ic_sq_nv.png"),
                                  width: 12,
                                  height: 12,
                                  fit: BoxFit.fill,
                                ).marginOnly(right: 3),
                              ],
                              Text(
                                '${calculateAge(widget.itemData.birthday)}・${widget.itemData.address}',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: ColorNatural.natural_8,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 计算年龄
  String calculateAge(String birthday) {
    if (birthday.isEmpty) return "0${"age_unit".tr}";
    try {
      final birthDate = DateTime.parse(birthday);
      final now = DateTime.now();
      int age = now.year - birthDate.year;
      if (now.month < birthDate.month ||
          (now.month == birthDate.month && now.day < birthDate.day)) {
        age--;
      }
      return age.toString() + "age_unit".tr;
    } catch (e) {
      return "0${"age_unit".tr}";
    }
  }
}
