plugins {
    id "com.android.application"
    id "kotlin-android"
    id "kotlin-kapt"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def flutterVersionCode = null
def flutterVersionName = null
if (flutter.hasProperty('versionCode')) {
    flutterVersionCode = flutter.versionCode
}
if (flutter.hasProperty('versionName')) {
    flutterVersionName = flutter.versionName
}

if (flutterVersionCode == null || flutterVersionName == null) {
    def localProperties = new Properties()
    rootProject.file('local.properties').withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }

    if (flutterVersionCode == null) {
        flutterVersionCode = localProperties.getProperty('flutter.versionCode')
    }
    if (flutterVersionName == null) {
        flutterVersionName = localProperties.getProperty('flutter.versionName')
    }
}

android {
    namespace "com.fluttercandies.wechatAssetsPickerExample"
    compileSdk flutter.compileSdkVersion
    ndkVersion flutter.ndkVersion

    defaultConfig {
        applicationId = "com.fluttercandies.wechatAssetsPickerExample"
        minSdk 21
        targetSdk flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    compileOptions {
        // Sets Java compatibility to Java 17
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    signingConfigs {
        forAll {
            storeFile file("${rootDir.absolutePath}/key.jks")
            storePassword 'picker'
            keyAlias 'picker'
            keyPassword 'picker'
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.forAll
        }
        profile {
            signingConfig signingConfigs.forAll
        }
        release {
            signingConfig signingConfigs.forAll
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation 'com.github.bumptech.glide:glide:4.15.0'
    kapt 'com.github.bumptech.glide:compiler:4.15.0'
}
