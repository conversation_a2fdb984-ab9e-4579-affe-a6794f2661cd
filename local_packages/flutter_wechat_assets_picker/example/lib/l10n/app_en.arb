{"@@locale": "en", "appTitle": "<PERSON><PERSON><PERSON>set Picker <PERSON>", "appVersion": "Version: {version}", "appVersionUnknown": "unknown", "navMulti": "Multi", "navSingle": "Single", "navCustom": "Custom", "selectedAssetsText": "Selected Assets", "pickMethodNotice": "Pickers in this page are located at the {dist}, defined by `pickMethods`.", "pickMethodImageName": "Image picker", "pickMethodImageDescription": "Only pick image from device.", "pickMethodVideoName": "Video picker", "pickMethodVideoDescription": "Only pick video from device. (Includes Live Photos on iOS and macOS.)", "pickMethodAudioName": "Audio picker", "pickMethodAudioDescription": "Only pick audio from device.", "pickMethodLivePhotoName": "Live Photo picker", "pickMethodLivePhotoDescription": "Only pick Live Photos from device.", "pickMethodCameraName": "Pick from camera", "pickMethodCameraDescription": "Allow to pick an asset through camera.", "pickMethodCameraAndStayName": "Pick from camera and stay", "pickMethodCameraAndStayDescription": "Take a photo or video with the camera picker, select the result and stay in the entities list.", "pickMethodCommonName": "Common picker", "pickMethodCommonDescription": "Pick images and videos.", "pickMethodThreeItemsGridName": "3 items grid", "pickMethodThreeItemsGridDescription": "Picker will served as 3 items on cross axis. (pageSize must be a multiple of the gridCount)", "pickMethodCustomFilterOptionsName": "Custom filter options", "pickMethodCustomFilterOptionsDescription": "Add filter options for the picker.", "pickMethodPrependItemName": "Prepend special item", "pickMethodPrependItemDescription": "A special item will prepend to the assets grid.", "pickMethodNoPreviewName": "No preview", "pickMethodNoPreviewDescription": "You cannot preview assets during the picking, the behavior is like the WhatsApp/MegaTok pattern.", "pickMethodKeepScrollOffsetName": "Keep scroll offset", "pickMethodKeepScrollOffsetDescription": "Pick assets from same scroll position.", "pickMethodChangeLanguagesName": "Change Languages", "pickMethodChangeLanguagesDescription": "Pass AssetPickerTextDelegate to change between languages (e.g. EnglishAssetPickerTextDelegate).", "pickMethodPreventGIFPickedName": "Prevent GIF being picked", "pickMethodPreventGIFPickedDescription": "Use selectPredicate to banned GIF picking when tapped.", "pickMethodCustomizableThemeName": "Customizable theme (ThemeData)", "pickMethodCustomizableThemeDescription": "Picking assets with the light theme or with a different color.", "pickMethodPathNameBuilderName": "Path name builder", "pickMethodPathNameBuilderDescription": "Add 🍭 after paths name.", "pickMethodWeChatMomentName": "WeChat Moment", "pickMethodWeChatMomentDescription": "Pick assets with images or only 1 video.", "pickMethodCustomImagePreviewThumbSizeName": "Custom image preview thumb size", "pickMethodCustomImagePreviewThumbSizeDescription": "You can reduce the thumb size to get faster load speed.", "customPickerNotice": "This page contains customized pickers with different asset types, different UI layouts, or some use case for specific apps. Contribute to add your custom picker are welcomed.\nPickers in this page are located at the lib/customs/pickers folder.", "customPickerCallThePickerButton": "🎁 Call the Picker", "customPickerDirectoryAndFileName": "Directory+File picker", "customPickerDirectoryAndFileDescription": "This is a custom picker built for `File`.\nBy browsing this picker, we want you to know that you can build your own picker components using the entity's type you desired.\n\nIn this page, picker will grab files from `getApplicationDocumentsDirectory`, then check whether it contains images. Put files into the path to see how this custom picker work.", "customPickerMultiTabName": "Multi tab picker", "customPickerMultiTabDescription": "The picker contains multiple tab with different types of assets for the picking at the same time.", "customPickerMultiTabTab1": "All", "customPickerMultiTabTab2": "Videos", "customPickerMultiTabTab3": "Images", "customPickerInstagramLayoutName": "Instagram layout picker", "customPickerInstagramLayoutDescription": "The picker reproduces Instagram layout with preview and scroll animations. It's also published as the package insta_assets_picker."}