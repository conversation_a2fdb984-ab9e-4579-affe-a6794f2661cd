{"@@locale": "zh", "appTitle": "<PERSON><PERSON><PERSON>set Picker 示例", "appVersion": "版本：{version}", "appVersionUnknown": "未知", "navMulti": "多选", "navSingle": "单选", "navCustom": "自定义", "selectedAssetsText": "已选的资源", "pickMethodNotice": "该页面的所有选择器的代码位于 {dist}，由 `pickMethods` 定义。", "pickMethodCommonName": "常用选择", "pickMethodCommonDescription": "选择图片和视频。", "pickMethodImageName": "图片选择", "pickMethodImageDescription": "仅选择图片。", "pickMethodVideoName": "视频选择", "pickMethodVideoDescription": "仅选择视频。", "pickMethodAudioName": "音频选择", "pickMethodAudioDescription": "仅选择音频。", "pickMethodLivePhotoName": "实况图片选择", "pickMethodLivePhotoDescription": "仅选择实况图片。", "pickMethodCameraName": "从相机生成选择", "pickMethodCameraDescription": "通过相机拍照生成并选择资源", "pickMethodCameraAndStayName": "从相机生成选择并停留", "pickMethodCameraAndStayDescription": "通过相机拍照生成选择资源，并停留在选择界面。", "pickMethodThreeItemsGridName": "横向 3 格", "pickMethodThreeItemsGridDescription": "选择器每行为 3 格。（pageSize 必须为 gridCount 的倍数）", "pickMethodCustomFilterOptionsName": "自定义过滤条件", "pickMethodCustomFilterOptionsDescription": "为选择器添加自定义过滤条件。", "pickMethodPrependItemName": "往网格前插入 widget", "pickMethodPrependItemDescription": "网格的靠前位置会添加一个自定义的 widget。", "pickMethodNoPreviewName": "禁止预览", "pickMethodNoPreviewDescription": "无法预览选择的资源，与 WhatsApp/MegaTok 的行为类似。", "pickMethodKeepScrollOffsetName": "保持滚动位置", "pickMethodKeepScrollOffsetDescription": "可以从上次滚动到的位置再次开始选择。", "pickMethodChangeLanguagesName": "更改语言", "pickMethodChangeLanguagesDescription": "传入 AssetPickerTextDelegate 手动更改选择器的语言（例如 EnglishAssetPickerTextDelegate）。", "pickMethodPreventGIFPickedName": "禁止选择 GIF 图片", "pickMethodPreventGIFPickedDescription": "通过 selectPredicate 来禁止 GIF 图片在点击时被选择。", "pickMethodCustomizableThemeName": "自定义主题 (ThemeData)", "pickMethodCustomizableThemeDescription": "可以用亮色或其他颜色及自定义的主题进行选择。", "pickMethodPathNameBuilderName": "构建路径名称", "pickMethodPathNameBuilderDescription": "在路径后添加 🍭 进行自定义。", "pickMethodWeChatMomentName": "微信朋友圈模式", "pickMethodWeChatMomentDescription": "允许选择图片或仅 1 个视频。", "pickMethodCustomImagePreviewThumbSizeName": "自定义图片预览的缩略图大小", "pickMethodCustomImagePreviewThumbSizeDescription": "通过降低缩略图的质量来获得更快的加载速度。", "customPickerNotice": "本页面包含了多种方式、不同界面和特定应用的自定义选择器。欢迎贡献添加你自定义的选择器。\n该页面的所有选择器的代码位于 lib/customs/pickers 目录。", "customPickerCallThePickerButton": "🎁 开始选择资源", "customPickerDirectoryAndFileName": "Directory+File 选择器", "customPickerDirectoryAndFileDescription": "为 `File` 构建的自定义选择器。\n通过阅读该选择器的源码，你可以学习如何完全以你自定义的资源类型来构建并选择器的界面。\n\n该选择器会从 `getApplicationDocumentsDirectory` 目录获取资源，然后检查它是否包含图片。你需要将图片放在该目录来查看选择器的效果。", "customPickerMultiTabName": "多 Tab 选择器", "customPickerMultiTabDescription": "该选择器会以多 Tab 的形式同时展示多种资源类型的选择器。", "customPickerMultiTabTab1": "全部", "customPickerMultiTabTab2": "视频", "customPickerMultiTabTab3": "图片", "customPickerInstagramLayoutName": "Instagram 布局的选择器", "customPickerInstagramLayoutDescription": "该选择器以 Instagram 的布局模式构建，在选择时可以同时预览。其已发布为单独的 package：insta_assets_picker。"}