<!-- Copyright 2019 The FlutterCandies author. All rights reserved.
Use of this source code is governed by an Apache license
that can be found in the LICENSE file. -->

# WeChat Assets Picker example

This is the example for the `wechat_assets_picker` package.

We've put multiple common usage
with the packages in the example.
You can both found `List<PickMethod> pickMethods` in
[here](lib/pages/multi_assets_page.dart)
and [here](lib/pages/single_assets_page.dart),
which provide methods in multiple picking and single picking mode.
Assets will be stored temporary and displayed at the below of the page.
