// Copyright 2019 The FlutterCandies author. All rights reserved.
// Use of this source code is governed by an Apache license that can be found
// in the LICENSE file.

import 'package:photo_manager/photo_manager.dart';

const String packageName = 'wechat_assets_picker';

const int defaultAssetsPerPage = 8;
const int defaultMaxAssetsCount = 9;

const ThumbnailSize defaultAssetGridPreviewSize = ThumbnailSize.square(200);
const ThumbnailSize defaultPathThumbnailSize = ThumbnailSize.square(80);
