---
name: Feature request (功能请求)
about: Request a new feature that the package didn't include. (请求一个依赖并未包含的功能)
title: "[Feature] Request a feature with something"
labels: feature, await investigate

---

**Version information**
 - Device: *e.g. iPhone X*
 - OS: *e.g. iOS 14.7.1*
 - Package Version: *e.g. v2.4.1*
 - Flutter Version: *e.g. v2.5.0*

**Is your feature request related to a problem?**
<!-- A clear and concise description if it's related to an exist problem.
     如果与已有问题有关，请准确描述。-->

**Describe the solution you'd like**
<!-- A clear and concise description of what you want to happen.
     描述你期望的解决方案。-->

**Describe alternatives you've considered**
<!-- A clear and concise description of any alternative solutions
     or features you've considered.
     准确描述其他解决方案或你的疑虑。 -->

**Additional context**
<!-- Add any other context or screenshots about the feature request here.
     请提供任何你认为需要的附加内容。 -->
