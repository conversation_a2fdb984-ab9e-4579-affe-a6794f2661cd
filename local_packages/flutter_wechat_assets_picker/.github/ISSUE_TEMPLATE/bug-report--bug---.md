---
name: Bug report (BUG模板)
about: Create a bug report helping us fix it. (创建一个 BUG 报告以帮助我们进行修复)
title: "[BUG] Error with something"
labels: await investigate, bug

---

**Describe the bug**
<!-- A clear and concise description of what the bug is.
     请用精炼的语句准确描述你遇到的BUG。-->

**How to reproduce**
<!-- Please **provide a minimum demo** rather than
     **a full project or a incomplete pages**.
     Otherwise, we won't accept your request.
     请提供一个 **最简单的 demo** 用于复现，而不要提供 **整个项目或者不完整的页面**，
     否则我们不会受理你的问题。-->

Steps to reproduce the behavior:
<!-- 描述复现步骤 -->

1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. Error occurred.

**Expected behavior**
<!-- A clear and concise description of what you expected to happen.
     描述你期望的行为。-->

**Screenshots (If contains)**
<!-- If applicable, add screenshots to help explain your problem.
     如有相关截图，请提供它们用于解释问题所在。-->

**Version information**

- Device: [e.g. iPhone X]
- OS: [e.g. iOS 14.7.1]
- Package Version: [e.g. 2.4.1]
- Flutter Version: [e.g. v2.5.0]

**Additional context**
<!-- Add any other context about the problem here.
     在此提供更多的内容。 -->
