name: Runnable (stable)

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

env:
  MINIMUM_FLUTTER_VERSION: '3.22.3'

jobs:
  analyze:
    name: Analyze on ${{ matrix.os }} with ${{ matrix.flutter-version }} Flutter
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ ubuntu-latest ]
        flutter-version: [ min, latest ]
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: '17'
      - uses: flutter-actions/setup-flutter@v4
        with:
          channel: stable
          version: ${{ matrix.flutter-version == 'min' && env.MINIMUM_FLUTTER_VERSION || 'latest' }}
      - name: Log Dart/Flutter versions
        run: |
          dart --version
          flutter --version
      - name: Prepare dependencies
        run: flutter pub get
      - name: Check Dart code formatting
        if: matrix.flutter-version != 'min'
        run: dart format . -o none --set-exit-if-changed
      - name: Analyze Dart code
        run: flutter analyze .
      - name: Run tests
        run: flutter test
      - name: Publish dry-run
        if: matrix.flutter-version != 'min'
        run: dart pub publish --dry-run
      - name: Generate docs
        if: matrix.flutter-version != 'min'
        run: |
          dart pub global activate dartdoc
          dart pub global run dartdoc

  test_iOS:
    needs: analyze
    name: Test iOS with ${{ matrix.flutter-version }} Flutter
    runs-on: macos-latest
    strategy:
      matrix:
        flutter-version: [ min, latest ]
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: '17'
      - uses: flutter-actions/setup-flutter@v4
        with:
          channel: stable
          version: ${{ matrix.flutter-version == 'min' && env.MINIMUM_FLUTTER_VERSION || 'latest' }}
      - run: dart --version
      - run: flutter --version
      - run: flutter pub get
      - run: cd example; flutter build ios --no-codesign

  test_android:
    needs: analyze
    name: Test Android with ${{ matrix.flutter-version }} Flutter
    runs-on: ubuntu-latest
    strategy:
      matrix:
        flutter-version: [ min, latest ]
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: '17'
      - uses: flutter-actions/setup-flutter@v4
        with:
          cache: true
          channel: stable
          version: ${{ matrix.flutter-version == 'min' && env.MINIMUM_FLUTTER_VERSION || 'latest' }}
      - run: dart --version
      - run: flutter --version
      - run: flutter pub get
      - run: flutter build apk --debug
        working-directory: ${{ github.workspace }}/example
