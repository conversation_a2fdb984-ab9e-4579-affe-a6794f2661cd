                        -H/Users/<USER>/projcet/flutter_sdk/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-<PERSON>ANDROID_PLATFORM=android-21
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=/Users/<USER>/projcet/android_sdk/ndk/25.1.8937393
-DCMAKE_ANDROID_NDK=/Users/<USER>/projcet/android_sdk/ndk/25.1.8937393
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/projcet/android_sdk/ndk/25.1.8937393/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/projcet/android_sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/projcet/projcet/flutter_projcet/foreign_friends/build/app/intermediates/cxx/Debug/3n1e3n11/obj/arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/projcet/projcet/flutter_projcet/foreign_friends/build/app/intermediates/cxx/Debug/3n1e3n11/obj/arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-B/Users/<USER>/projcet/projcet/flutter_projcet/foreign_friends/android/app/.cxx/Debug/3n1e3n11/arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2