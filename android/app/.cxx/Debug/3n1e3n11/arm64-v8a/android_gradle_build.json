{"buildFiles": ["/Users/<USER>/projcet/flutter_sdk/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/projcet/android_sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/projcet/projcet/flutter_projcet/foreign_friends/android/app/.cxx/Debug/3n1e3n11/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/projcet/android_sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/projcet/projcet/flutter_projcet/foreign_friends/android/app/.cxx/Debug/3n1e3n11/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "/Users/<USER>/projcet/android_sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang.lld", "cppCompilerExecutable": "/Users/<USER>/projcet/android_sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": []}